const request = require("supertest");
const app = require("../app.config");
// const app = require('../index')
describe("Get Endpoints", () => {
  it("Test If running", async (done) => {
    const res = await request(app).get("/").send();
    // .send({
    //   sessionId: 1,
    //   title: 'test is cool',
    // })
    expect(res.statusCode).toEqual(200);
    expect(res.text).toEqual("Hello World!");
    done();
  });
  it("Test sending message", async (done) => {
    const res = await request(app)
      .post("/webhook")
      .send({
        object: "page",
        entry: [
          {
            id: "158625401348031",
            time: 1587575252627,
            messaging: [
              {
                sender: { id: "1510352682393194" },
                recipient: { id: "158625401348031" },
                timestamp: 1587575252468,
                message: {
                  mid: "m_hcv0C1G1kCNezoonvG4qY48XcpECsnIb-iLboQUg6Q8BMAKJyX9GeBR2KtABs0vsVlkbv0LUNeQ8JQHDOCf-QA",
                  text: "**********************",
                },
              },
            ],
          },
        ],
      });
    expect(res.statusCode).toEqual(200);
    done();
  });
  it("Test attachment message", async (done) => {
    const res = await request(app)
      .post("/webhook")
      .send({
        object: "page",
        entry: [
          {
            id: "158625401348031",
            time: 1587575252627,
            messaging: [
              {
                sender: { id: "1510352682393194" },
                recipient: { id: "158625401348031" },
                timestamp: 1587578848797,
                message: {
                  mid: "m_aZ8WySNVQq6lW8Xc6_L3MI8XcpECsnIb-iLboQUg6Q9OQiht8SciNGBUYLLY_7PsCwQCgdP1ebLGoM-xtPy5Xg",
                  attachments: [
                    {
                      type: "image",
                      payload: {
                        url: "https://scontent.xx.fbcdn.net/v/t39.1997-6/cp0/39178562_1505197616293642_5411344281094848512_n.png?_nc_cat=1&_nc_sid=ac3552&_nc_oc=AQkckMTbyg6llarz4RSo_DTnFyDP13Cktk60lxrsn3IdfCU_kzLmYKBviiN3aVFTFZ07GPEPYwH-v8cunTDUA2fq&_nc_ad=z-m&_nc_cid=0&_nc_zor=9&_nc_ht=scontent.xx&oh=553af584d7d1ba2fd424203a86b03bec&oe=5EC44049",
                        sticker_id: 369239263222822,
                      },
                    },
                  ],
                },
              },
            ],
          },
        ],
      });
    expect(res.statusCode).toEqual(200);
    done();
  });
});

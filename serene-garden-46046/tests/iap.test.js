const request = require('supertest');
const mongoose = require('mongoose');
const app = require('../app.config');

// Test data
const testDeviceId = 'test-device-12345';
const testChannelId = 'test-channel';
const testTransactionId = 'test-transaction-12345';

// Mock receipt data
const mockAppleReceipt = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9'; // Mock base64 receipt
const mockGooglePurchaseToken = 'mock-google-purchase-token-12345';

describe('IAP API Tests', () => {
  beforeAll(async () => {
    // Connect to test database
    if (mongoose.connection.readyState === 0) {
      await mongoose.connect(process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/friendly-chat-test');
    }
  });

  afterAll(async () => {
    // Clean up test data and close connection
    await mongoose.connection.close();
  });

  beforeEach(async () => {
    // Clean up test data before each test
    const User = require('../scripts/DB/schemes/user/user');
    const Purchase = require('../scripts/DB/schemes/iap/purchase');
    
    await User.deleteMany({ deviceId: testDeviceId });
    await Purchase.deleteMany({ transactionId: testTransactionId });
  });

  describe('Health Check', () => {
    test('GET /api/iap/health should return healthy status', async () => {
      const response = await request(app)
        .get('/api/iap/health')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('healthy');
    });
  });

  describe('Purchase Verification', () => {
    test('POST /api/iap/verify-purchase should require deviceId and channelId', async () => {
      const response = await request(app)
        .post('/api/iap/verify-purchase')
        .send({
          platform: 'ios',
          productId: 'ad_free_subscription',
          receipt: mockAppleReceipt
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('deviceId and channelId are required');
    });

    test('POST /api/iap/verify-purchase should validate required fields', async () => {
      const response = await request(app)
        .post('/api/iap/verify-purchase')
        .send({
          deviceId: testDeviceId,
          channelId: testChannelId
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Missing required fields');
    });

    test('POST /api/iap/verify-purchase should handle iOS purchase', async () => {
      const response = await request(app)
        .post('/api/iap/verify-purchase')
        .send({
          platform: 'ios',
          productId: 'ad_free_subscription',
          receipt: mockAppleReceipt,
          deviceId: testDeviceId,
          channelId: testChannelId
        });

      // Note: This will likely fail in test environment due to invalid receipt
      // but we're testing the API structure
      expect(response.body).toHaveProperty('success');
    });

    test('POST /api/iap/verify-purchase should require purchaseToken for Android', async () => {
      const response = await request(app)
        .post('/api/iap/verify-purchase')
        .send({
          platform: 'android',
          productId: 'ad_free_subscription',
          receipt: 'mock-receipt',
          deviceId: testDeviceId,
          channelId: testChannelId
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('purchaseToken is required for Android');
    });
  });

  describe('User Status', () => {
    test('GET /api/iap/status/:deviceId/:channelId should return user status', async () => {
      // First create a user
      const User = require('../scripts/DB/schemes/user/user.ctrl');
      await User.find({ deviceId: testDeviceId, channelId: testChannelId });

      const response = await request(app)
        .get(`/api/iap/status/${testDeviceId}/${testChannelId}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.status).toHaveProperty('hasAdFreeAccess');
      expect(response.body.status).toHaveProperty('hasActiveSubscription');
    });

    test('GET /api/iap/status/:deviceId/:channelId should handle missing parameters', async () => {
      const response = await request(app)
        .get('/api/iap/status//')
        .expect(404); // Route not found due to empty parameters
    });
  });

  describe('Status Refresh', () => {
    test('POST /api/iap/refresh-status should refresh user status', async () => {
      // First create a user
      const User = require('../scripts/DB/schemes/user/user.ctrl');
      await User.find({ deviceId: testDeviceId, channelId: testChannelId });

      const response = await request(app)
        .post('/api/iap/refresh-status')
        .send({
          deviceId: testDeviceId,
          channelId: testChannelId
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('refreshed');
      expect(response.body.status).toHaveProperty('hasAdFreeAccess');
    });
  });

  describe('Purchase History', () => {
    test('GET /api/iap/purchases/:deviceId/:channelId should return purchase history', async () => {
      // First create a user
      const User = require('../scripts/DB/schemes/user/user.ctrl');
      const user = await User.find({ deviceId: testDeviceId, channelId: testChannelId });

      const response = await request(app)
        .get(`/api/iap/purchases/${testDeviceId}/${testChannelId}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.purchases).toBeInstanceOf(Array);
      expect(response.body.pagination).toHaveProperty('limit');
      expect(response.body.pagination).toHaveProperty('offset');
      expect(response.body.pagination).toHaveProperty('total');
    });

    test('GET /api/iap/purchases/:deviceId/:channelId should support pagination', async () => {
      // First create a user
      const User = require('../scripts/DB/schemes/user/user.ctrl');
      await User.find({ deviceId: testDeviceId, channelId: testChannelId });

      const response = await request(app)
        .get(`/api/iap/purchases/${testDeviceId}/${testChannelId}?limit=5&offset=10`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.pagination.limit).toBe(5);
      expect(response.body.pagination.offset).toBe(10);
    });
  });

  describe('Webhook Endpoints', () => {
    test('POST /api/iap/webhook/apple should accept Apple webhooks', async () => {
      const mockAppleWebhook = {
        notification_type: 'DID_RENEW',
        unified_receipt: {
          receipt: { bundle_id: 'com.friendlychatclient' },
          latest_receipt_info: [{
            transaction_id: testTransactionId,
            product_id: 'ad_free_subscription'
          }]
        }
      };

      const response = await request(app)
        .post('/api/iap/webhook/apple')
        .send(mockAppleWebhook);

      // Should accept the webhook (even if processing fails due to test data)
      expect([200, 400, 500]).toContain(response.status);
    });

    test('POST /api/iap/webhook/google should accept Google webhooks', async () => {
      const mockGoogleWebhook = {
        message: {
          data: Buffer.from(JSON.stringify({
            version: '1.0',
            packageName: 'com.friendlychatclient',
            subscriptionNotification: {
              notificationType: 2, // SUBSCRIPTION_RENEWED
              subscriptionId: 'ad_free_subscription',
              purchaseToken: mockGooglePurchaseToken
            }
          })).toString('base64')
        }
      };

      const response = await request(app)
        .post('/api/iap/webhook/google')
        .send(mockGoogleWebhook);

      // Should accept the webhook (even if processing fails due to test data)
      expect([200, 400, 500]).toContain(response.status);
    });
  });

  describe('Rate Limiting', () => {
    test('Should enforce rate limits on purchase verification', async () => {
      const requests = [];
      
      // Make multiple requests quickly
      for (let i = 0; i < 12; i++) {
        requests.push(
          request(app)
            .post('/api/iap/verify-purchase')
            .send({
              platform: 'ios',
              productId: 'ad_free_subscription',
              receipt: mockAppleReceipt,
              deviceId: `${testDeviceId}-${i}`,
              channelId: testChannelId
            })
        );
      }

      const responses = await Promise.all(requests);
      
      // At least one request should be rate limited (429 status)
      const rateLimitedResponses = responses.filter(res => res.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });
  });

  describe('Error Handling', () => {
    test('Should handle invalid JSON gracefully', async () => {
      const response = await request(app)
        .post('/api/iap/verify-purchase')
        .set('Content-Type', 'application/json')
        .send('invalid json')
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    test('Should require JSON content type for POST requests', async () => {
      const response = await request(app)
        .post('/api/iap/verify-purchase')
        .set('Content-Type', 'text/plain')
        .send('test')
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Content-Type must be application/json');
    });
  });
});

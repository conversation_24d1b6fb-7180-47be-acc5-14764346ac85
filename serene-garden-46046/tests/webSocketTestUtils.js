/**
 * Creates and starts a WebSocket server from a simple http server for testing purposes.
 * @param {number} port Port for the server to listen on
 * @returns {Promise<Server>} The created server
 */
function startServer(port) {
  const server = require("../app.socket.io").server;
  server.listen(port, () =>
    console.log(`Example app listening on port ${port}!`)
  );

  return server;
}

export { startServer };

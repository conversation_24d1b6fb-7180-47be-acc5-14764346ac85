import SocketService from "./websocketClientTest";
import { startServer } from "./webSocketTestUtils";

const port = 3000 + Number(process.env.JEST_WORKER_ID);

describe("WebSocket Server", () => {
  let server;

  beforeAll(async () => {
    server = startServer(port);
  });

  afterAll(() => {
    SocketService.close();
    server.close();
  });

  test("Server echoes the message it receives from client", async () => {
    // Create test client
    SocketService.init(1, "test1");
    SocketService.emit("chat message", { id: 1, text: "test" });
    expect("responseMessage").toBe("responseMessage");
  });
});

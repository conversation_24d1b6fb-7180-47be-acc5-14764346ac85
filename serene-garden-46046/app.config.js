const express = require("express");
const app = express(),
  bodyParser = require("body-parser");
const {
  helloWorld,
  verify,
  getWebhook,
  postWebhook,
} = require("./scripts/requestReciever");
const iapRoutes = require("./scripts/iap/iapRoutes");
app.use(bodyParser.urlencoded({ extended: false }));
app.use(bodyParser.json());
app.use(function (req, res, next) {
  res.setHeader("Access-Control-Allow-Origin", "*");
  res.header("Access-Control-Allow-Credentials", true);
  res.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE");
  res.setHeader(
    "Access-Control-Allow-Headers",
    "X-Requested-With, content-type, Authorization"
  );
  next();
});
app.get("/", helloWorld);
app.get("/verify", verify);
app.get("/webhook", getWebhook);
app.post("/webhook", postWebhook);

// IAP routes
app.use("/api/iap", iapRoutes);
module.exports = app;

var { replyBack } = require("./requestHandler");

module.exports.helloWorld = async (req, res) => {
  const socketIO = require("../app.socket.io");
  let sockets = await socketIO.io.fetchSockets();
  return res.send(`Hello World! Connected users: ${sockets.length} | Max concurrent users: ${socketIO.maxConnectedUsers()}`);
};
module.exports.verify = (req, res) => res.send("service is up and running!");
module.exports.getWebhook = (req, res) =>
  req.query["hub.verify_token"] == process.env.VERIFY_TOKEN
    ? res.send(req.query["hub.challenge"])
    : res.send("Hello There");

// Accepts POST requests at /webhook endpoint
module.exports.postWebhook = (req, res) => {
  let body = req.body;

  //
  if (body.object === "page") {
    body.entry.forEach(function (entry) {
      // Gets the body of the webhook event
      let webhook_event = entry.messaging[0];
      replyBack(webhook_event);
      res.status(200).send("EVENT_RECEIVED");
    });
    // Return a '200 OK' response to all events
  } else {
    // Return a '404 Not Found' if event is not from a page subscription
    res.sendStatus(404);
  }
};

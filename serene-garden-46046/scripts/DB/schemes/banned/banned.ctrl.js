const Banned = require("./banned");
module.exports.checkIfbanned = async (user) => {
  let bannedUser = await fetch(user);
  if (!bannedUser) return false;
  if (bannedUser.banEnd > Date.now()) {
    return { status: true, banEnd: bannedUser.banEnd };
  } else {
    remove(user);
    return { status: false };
  }
};
function fetch(sender) {
  return Banned.findOne({
    user: sender,
  })
    .populate({
      path: "user",
    })
    .exec();
}
exports.add = (sender, banEnd) => {
  return Banned.findOneAndUpdate(
    { user: sender },
    { user: sender, banEnd: banEnd },
    { upsert: true, new: true }
  )
    .populate("user")
    .exec();
};
function remove(sender) {
  return Banned.deleteOne({ user: sender }).exec();
}

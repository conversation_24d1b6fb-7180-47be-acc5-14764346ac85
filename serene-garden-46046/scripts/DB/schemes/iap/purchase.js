var mongoose = require("mongoose");
var Schema = mongoose.Schema;

var purchaseSchema = new Schema(
  {
    user: { type: Schema.Types.ObjectId, ref: "User", required: true },
    
    // Purchase identification
    transactionId: { type: String, required: true, unique: true },
    originalTransactionId: { type: String },
    productId: { type: String, required: true },
    
    // Platform information
    platform: { type: String, enum: ['ios', 'android'], required: true },
    
    // Purchase details
    purchaseDate: { type: Date, required: true },
    expiryDate: { type: Date }, // For subscriptions and time-limited purchases
    quantity: { type: Number, default: 1 },
    
    // Receipt information
    receiptData: { type: String, required: true }, // Base64 encoded receipt
    receiptValidated: { type: Boolean, default: false },
    validationDate: { type: Date },
    
    // Purchase status
    status: {
      type: String,
      enum: ['pending', 'active', 'expired', 'cancelled', 'refunded'],
      default: 'pending'
    },
    
    // Store-specific data
    storeData: {
      // Apple specific
      bundleId: String,
      applicationVersion: String,
      originalApplicationVersion: String,
      
      // Google specific
      orderId: String,
      packageName: String,
      purchaseToken: String,
      
      // Common
      environment: { type: String, enum: ['sandbox', 'production'] }
    },
    
    // Validation details
    validationResponse: Schema.Types.Mixed, // Store the full validation response
    lastValidationCheck: { type: Date },
    
    // Auto-renewal information (for subscriptions)
    isAutoRenewing: { type: Boolean, default: false },
    willRenew: { type: Boolean, default: true },
    
    // Cancellation information
    cancellationDate: { type: Date },
    cancellationReason: String,
    
    // Refund information
    refundDate: { type: Date },
    refundReason: String,
  },
  { 
    timestamps: true,
    // Index for efficient queries
    index: {
      user: 1,
      productId: 1,
      status: 1,
      expiryDate: 1
    }
  }
);

// Indexes for performance
purchaseSchema.index({ user: 1, productId: 1 });
purchaseSchema.index({ transactionId: 1 });
purchaseSchema.index({ status: 1, expiryDate: 1 });
purchaseSchema.index({ platform: 1, status: 1 });

// Instance methods
purchaseSchema.methods.isActive = function() {
  if (this.status !== 'active') return false;
  if (this.expiryDate && this.expiryDate < new Date()) return false;
  return true;
};

purchaseSchema.methods.isExpired = function() {
  if (this.expiryDate && this.expiryDate < new Date()) return true;
  return false;
};

// Static methods
purchaseSchema.statics.findActiveByUser = function(userId) {
  return this.find({
    user: userId,
    status: 'active',
    $or: [
      { expiryDate: { $exists: false } },
      { expiryDate: { $gt: new Date() } }
    ]
  });
};

purchaseSchema.statics.findByTransactionId = function(transactionId) {
  return this.findOne({ transactionId: transactionId });
};

module.exports = mongoose.model("Purchase", purchaseSchema);

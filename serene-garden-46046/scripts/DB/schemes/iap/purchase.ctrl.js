const Purchase = require("./purchase");
const RetryManager = require("../../../utils/retry");

// Create a new purchase record
exports.create = (purchaseData) => {
  return RetryManager.forDatabase(async () => {
    try {
      const purchase = new Purchase(purchaseData);
      const savedPurchase = await purchase.save();
      return savedPurchase;
    } catch (error) {
      console.error("Error creating purchase:", error);
      throw error;
    }
  });
};

// Find purchase by transaction ID with retry
exports.findByTransactionId = (transactionId) => {
  return RetryManager.forDatabase(() => {
    return Purchase.findByTransactionId(transactionId);
  });
};

// Find purchase by original transaction ID (for renewals)
exports.findByOriginalTransactionId = async (originalTransactionId) => {
  try {
    return await Purchase.findOne({ originalTransactionId });
  } catch (error) {
    console.error("Error finding purchase by original transaction ID:", error);
    throw error;
  }
};

// Find purchase by either transaction ID or original transaction ID
exports.findByAnyTransactionId = async (transactionId) => {
  try {
    // First try to find by transaction ID
    let purchase = await Purchase.findByTransactionId(transactionId);

    // If not found, try by original transaction ID
    if (!purchase) {
      purchase = await Purchase.findOne({ originalTransactionId: transactionId });
    }

    return purchase;
  } catch (error) {
    console.error("Error finding purchase by any transaction ID:", error);
    throw error;
  }
};

// Find all active purchases for a user
exports.findActiveByUser = async (userId) => {
  try {
    return await Purchase.findActiveByUser(userId);
  } catch (error) {
    console.error("Error finding active purchases for user:", error);
    throw error;
  }
};

// Update purchase status
exports.updateStatus = async (transactionId, status, additionalData = {}) => {
  try {
    const updateData = {
      status,
      lastValidationCheck: new Date(),
      ...additionalData
    };

    const result = await Purchase.findOneAndUpdate(
      { transactionId },
      updateData,
      { new: true }
    );
    return result;
  } catch (error) {
    console.error("Error updating purchase status:", error);
    throw error;
  }
};

// Validate and update purchase from receipt
exports.validateAndUpdate = async (transactionId, validationResponse) => {
  try {
    const updateData = {
      receiptValidated: true,
      validationDate: new Date(),
      validationResponse,
      lastValidationCheck: new Date()
    };

    // Determine status based on validation response
    // For Google Play, check purchaseState (0 = purchased, 1 = canceled)
    // For Apple, check status field
    const isActive = validationResponse.purchaseState === 0 || validationResponse.status === 0;
    updateData.status = isActive ? "active" : "pending";

    return await Purchase.findOneAndUpdate(
      { transactionId },
      updateData,
      { new: true }
    );
  } catch (error) {
    console.error("Error validating and updating purchase:", error);
    throw error;
  }
};

// Helper function to check subscription status
function checkSubscriptionStatus(activePurchases) {
  const subscription = activePurchases.find((p) =>
    p.productId === "ad_free_subscription" && p.isActive()
  );
  const oneMonth = activePurchases.find((p) =>
    p.productId === "one_month_ad_free" && p.isActive()
  );

  return {
    hasActiveSubscription: Boolean(subscription),
    hasAdFreeAccess: Boolean(subscription || oneMonth),
    subscriptionExpiry: subscription?.expiryDate || null,
    oneMonthExpiry: oneMonth?.expiryDate || null
  };
}

// Get user's current IAP status
exports.getUserIAPStatus = async (userId) => {
  try {
    const activePurchases = await Purchase.findActiveByUser(userId);
    const subscriptionStatus = checkSubscriptionStatus(activePurchases);

    return {
      ...subscriptionStatus,
      activePurchases: activePurchases.map((p) => ({
        productId: p.productId,
        expiryDate: p.expiryDate,
        status: p.status
      }))
    };
  } catch (error) {
    console.error("Error getting user IAP status:", error);
    throw error;
  }
};

// Handle subscription renewal
exports.handleRenewal = async (transactionId, newExpiryDate) => {
  try {
    const result = await Purchase.findOneAndUpdate(
      { transactionId },
      {
        expiryDate: newExpiryDate,
        status: "active",
        lastValidationCheck: new Date()
      },
      { new: true }
    );
    return result;
  } catch (error) {
    console.error("Error handling subscription renewal:", error);
    throw error;
  }
};

// Handle subscription cancellation
exports.handleCancellation = async (transactionId, cancellationReason) => {
  try {
    return await Purchase.findOneAndUpdate(
      { transactionId },
      {
        status: "cancelled",
        cancellationDate: new Date(),
        cancellationReason,
        willRenew: false,
        lastValidationCheck: new Date()
      },
      { new: true }
    );
  } catch (error) {
    console.error("Error handling subscription cancellation:", error);
    throw error;
  }
};

// Handle subscription refund
exports.handleRefund = async (transactionId, refundReason) => {
  try {
    return await Purchase.findOneAndUpdate(
      { transactionId },
      {
        status: "refunded",
        refundDate: new Date(),
        refundReason,
        willRenew: false,
        lastValidationCheck: new Date()
      },
      { new: true }
    );
  } catch (error) {
    console.error("Error handling subscription refund:", error);
    throw error;
  }
};

// Clean up expired purchases
exports.cleanupExpired = async () => {
  try {
    const result = await Purchase.updateMany(
      {
        status: "active",
        expiryDate: { $lt: new Date() }
      },
      {
        status: "expired",
        lastValidationCheck: new Date()
      }
    );

    console.log(`Cleaned up ${result.modifiedCount} expired purchases`);
    return result;
  } catch (error) {
    console.error("Error cleaning up expired purchases:", error);
    throw error;
  }
};

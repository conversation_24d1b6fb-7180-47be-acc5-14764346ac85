var mongoose = require("mongoose");
var Schema = mongoose.Schema;

var aschema = new Schema(
  {
    sessionId: String, //sessionID
    channelId: String,
    deviceId: String,
    name: String,
    gender: String,
    settings: { type: Schema.Types.ObjectId, ref: "Settings" },
    blockList: [{ type: Schema.Types.ObjectId, ref: "User" }],
    reportList: [{ type: Schema.Types.ObjectId, ref: "User" }],
    lastReported: {
      date: Date,
      count: Number,
    },
    banCount: { type: Number, default: 0 },

    // IAP-related fields
    iap: {
      // Current subscription status
      hasActiveSubscription: { type: Boolean, default: false },
      subscriptionExpiry: { type: Date },
      subscriptionProductId: { type: String },

      // One-time purchase status
      hasAdFreeAccess: { type: Boolean, default: false },
      oneMonthExpiry: { type: Date },

      // Purchase references
      activePurchases: [{ type: Schema.Types.ObjectId, ref: "Purchase" }],

      // Last validation check
      lastIAPCheck: { type: Date },

      // Platform preference for IAP
      preferredPlatform: { type: String, enum: ['ios', 'android'] }
    }
  },
  { timestamps: {} }
);
aschema.index(
  { updatedAt: 1 },
  {
    expireAfterSeconds: 30 * 24 * 60 * 60,
  }
);
module.exports = mongoose.model("User", aschema);

const Chatting = require("./chatting");
exports.create = function (sender, receipt) {
  var chatting = new Chatting();
  chatting.participants = [sender, receipt];
  return chatting.save();
};
exports.find = (sender) => {
  return Chatting.findOne({ participants: sender })
    .populate("participants")
    .exec();
};
exports.remove = (sender) => {
  return Chatting.findOneAndDelete({ participants: sender })
    .populate("participants")
    .exec();
};

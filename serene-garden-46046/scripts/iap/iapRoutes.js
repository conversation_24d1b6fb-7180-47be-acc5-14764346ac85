const express = require("express");
const router = express.Router();
const iapController = require("./iapController");
const {
  verifyAppleWebhook,
  verifyGoogleWebhook,
  webhookRateLimit,
  validateWebhookContentType,
  logWebhookRequest,
  webhookErrorHandler
} = require("../middleware/webhookAuth");
const { limiters } = require("../middleware/rateLimiter");
const { authMiddleware, validateRequest, securityHeaders } = require("../middleware/auth");

// Middleware for basic request logging
const logRequest = (req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
};

// Apply security and validation middleware to all IAP routes
router.use(securityHeaders);
router.use(validateRequest);
router.use(logRequest);

// Middleware for validating JSON content type for POST requests
const validateJsonContentType = (req, res, next) => {
  if (req.method === "POST" && !req.is("application/json")) {
    return res.status(400).json({
      success: false,
      error: "Content-Type must be application/json"
    });
  }
  next();
};

// Apply JSON validation to POST routes
router.use(validateJsonContentType);

// Health check endpoint (no auth required)
router.get("/health", iapController.healthCheck);

// Purchase verification endpoint (with strict rate limiting and device auth)
router.post("/verify-purchase",
  limiters.purchase,
  authMiddleware.validateDeviceAuth(),
  iapController.verifyPurchase
);

// Get user's current IAP status (with rate limiting and device auth)
router.get("/status/:deviceId/:channelId",
  limiters.status,
  authMiddleware.validateDeviceAuth(),
  iapController.getUserStatus
);

// Refresh user's IAP status (with rate limiting and device auth)
router.post("/refresh-status",
  limiters.status,
  authMiddleware.validateDeviceAuth(),
  iapController.refreshUserStatus
);

// Get user's purchase history (with rate limiting and device auth)
router.get("/purchases/:deviceId/:channelId",
  limiters.general,
  authMiddleware.validateDeviceAuth(),
  iapController.getPurchaseHistory
);

// Webhook endpoints for store notifications with security middleware
router.post("/webhook/apple",
  logWebhookRequest,
  webhookRateLimit,
  validateWebhookContentType,
  verifyAppleWebhook,
  iapController.handleAppleWebhook
);

router.post("/webhook/google",
  logWebhookRequest,
  webhookRateLimit,
  validateWebhookContentType,
  verifyGoogleWebhook,
  iapController.handleGoogleWebhook
);

// Error handling middleware for IAP routes
router.use(webhookErrorHandler);
router.use((error, req, res, next) => {
  console.error("IAP Route Error:", error);

  if (error.type === "entity.parse.failed") {
    return res.status(400).json({
      success: false,
      error: "Invalid JSON in request body"
    });
  }

  res.status(500).json({
    success: false,
    error: "Internal server error",
    message: process.env.NODE_ENV === "development" ? error.message : "Something went wrong"
  });
});

module.exports = router;

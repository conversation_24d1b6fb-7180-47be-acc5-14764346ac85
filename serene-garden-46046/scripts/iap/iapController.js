/* eslint-disable class-methods-use-this */
/* eslint-disable max-statements */
const iapService = require("../services/iapService");
const UserController = require("../DB/schemes/user/user.ctrl");

class IAPController {
  /**
   * Verify a purchase receipt
   * POST /api/iap/verify-purchase
   */
  async verifyPurchase(req, res) {
    try {
      const { platform, productId, receipt, purchaseToken, deviceId, channelId } = req.body;

      // Validate required fields
      if (!platform || !productId || !receipt || !deviceId || !channelId) {
        return res.status(400).json({
          success: false,
          error: "Missing required fields: platform, productId, receipt, deviceId, channelId"
        });
      }

      if (platform === "android" && !purchaseToken) {
        return res.status(400).json({
          success: false,
          error: "purchaseToken is required for Android purchases"
        });
      }

      // Find or create user
      const user = await UserController.find({ deviceId, channelId });
      if (!user) {
        return res.status(404).json({
          success: false,
          error: "User not found"
        });
      }

      // Verify the purchase
      const verificationResult = await iapService.verifyPurchase({
        platform,
        productId,
        receipt,
        purchaseToken,
        userId: user._id,
        deviceId
      });

      if (!verificationResult.success) {
        return res.status(400).json({
          success: false,
          error: verificationResult.error,
          errorCode: verificationResult.errorCode
        });
      }

      // Return success with purchase and user status
      res.json({
        success: true,
        message: "Purchase verified successfully",
        purchase: {
          transactionId: verificationResult.purchase.transactionId,
          productId: verificationResult.purchase.productId,
          status: verificationResult.purchase.status,
          expiryDate: verificationResult.purchase.expiryDate
        },
        userStatus: verificationResult.userStatus
      });

    } catch (error) {
      console.error("Error in verifyPurchase:", error);
      res.status(500).json({
        success: false,
        error: "Internal server error",
        message: error.message
      });
    }
  }

  /**
   * Get user's current IAP status
   * GET /api/iap/status/:deviceId/:channelId
   */
  async getUserStatus(req, res) {
    try {
      const { deviceId, channelId } = req.params;
      console.log("Received IAP status request for:", deviceId, channelId);
      if (!deviceId || !channelId) {
        return res.status(400).json({
          success: false,
          error: "deviceId and channelId are required"
        });
      }

      // Find user
      const user = await UserController.find({ deviceId, channelId });
      if (!user) {
        return res.status(404).json({
          success: false,
          error: "User not found"
        });
      }

      // Get IAP status
      const iapStatus = await iapService.getUserIAPStatus(user._id);
      // console.log("iapStatus:", iapStatus);

      res.json({
        success: true,
        status: iapStatus
      });

    } catch (error) {
      console.error("Error in getUserStatus:", error);
      res.status(500).json({
        success: false,
        error: "Internal server error",
        message: error.message
      });
    }
  }

  /**
   * Refresh user's IAP status by re-validating active purchases
   * POST /api/iap/refresh-status
   */
  async refreshUserStatus(req, res) {
    try {
      const { deviceId, channelId } = req.body;

      if (!deviceId || !channelId) {
        return res.status(400).json({
          success: false,
          error: "deviceId and channelId are required"
        });
      }

      // Find user
      const user = await UserController.find({ deviceId, channelId });
      if (!user) {
        return res.status(404).json({
          success: false,
          error: "User not found"
        });
      }

      // Update user's IAP status
      const updatedStatus = await iapService.updateUserIAPStatus(user._id);

      res.json({
        success: true,
        message: "Status refreshed successfully",
        status: updatedStatus
      });

    } catch (error) {
      console.error("Error in refreshUserStatus:", error);
      res.status(500).json({
        success: false,
        error: "Internal server error",
        message: error.message
      });
    }
  }

  /**
   * Handle Apple App Store Server-to-Server notifications
   * POST /api/iap/webhook/apple
   */
  async handleAppleWebhook(req, res) {
    try {
      console.log("Received Apple webhook notification");

      const notificationPayload = req.body;

      // Process the notification
      const result = await iapService.handleStoreNotification("ios", notificationPayload);

      if (result.success) {
        res.status(200).json({ status: "ok" });
      } else {
        console.error("Failed to process Apple notification:", result.error);
        res.status(400).json({ error: result.error });
      }

    } catch (error) {
      console.error("Error in handleAppleWebhook:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  }

  /**
   * Handle Google Play Developer Notifications
   * POST /api/iap/webhook/google
   */
  async handleGoogleWebhook(req, res) {
    try {
      console.log("Received Google Play webhook notification");

      const notificationPayload = req.body;

      // Process the notification
      const result = await iapService.handleStoreNotification("android", notificationPayload);

      if (result.success) {
        res.status(200).json({ status: "ok" });
      } else {
        console.error("Failed to process Google notification:", result.error);
        res.status(400).json({ error: result.error });
      }

    } catch (error) {
      console.error("Error in handleGoogleWebhook:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  }

  /**
   * Get purchase history for a user
   * GET /api/iap/purchases/:deviceId/:channelId
   */
  async getPurchaseHistory(req, res) {
    try {
      const { deviceId, channelId } = req.params;
      const { limit = 10, offset = 0 } = req.query;

      if (!deviceId || !channelId) {
        return res.status(400).json({
          success: false,
          error: "deviceId and channelId are required"
        });
      }

      // Find user
      const user = await UserController.find({ deviceId, channelId });
      if (!user) {
        return res.status(404).json({
          success: false,
          error: "User not found"
        });
      }

      // Get purchase history (you'll need to implement this in PurchaseController)
      const Purchase = require("../DB/schemes/iap/purchase");
      const purchases = await Purchase.find({ user: user._id })
        .sort({ createdAt: -1 })
        .limit(parseInt(limit))
        .skip(parseInt(offset))
        .select("transactionId productId status purchaseDate expiryDate platform");

      res.json({
        success: true,
        purchases: purchases,
        pagination: {
          limit: parseInt(limit),
          offset: parseInt(offset),
          total: await Purchase.countDocuments({ user: user._id })
        }
      });

    } catch (error) {
      console.error("Error in getPurchaseHistory:", error);
      res.status(500).json({
        success: false,
        error: "Internal server error",
        message: error.message
      });
    }
  }

  /**
   * Health check endpoint for IAP service
   * GET /api/iap/health
   */
  async healthCheck(req, res) {
    try {
      res.json({
        success: true,
        message: "IAP service is healthy",
        timestamp: new Date().toISOString(),
        version: "1.0.0"
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: "Service unhealthy",
        message: error.message
      });
    }
  }
}

module.exports = new IAPController();

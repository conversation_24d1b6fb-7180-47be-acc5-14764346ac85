const { circuitBreakerManager } = require("./circuitBreakerManager");

/**
 * Health check system for IAP dependencies
 * Monitors external services and database connectivity
 */
class HealthCheckManager {
    constructor() {
        this.checks = new Map();
        this.lastCheckResults = new Map();
        this.checkInterval = 30000; // 30 seconds
        this.intervalId = null;
    }

    /**
     * Register a health check
     */
    registerCheck(name, checkFunction, options = {}) {
        this.checks.set(name, {
            name,
            checkFunction,
            timeout: options.timeout || 5000,
            critical: options.critical !== false, // Default to critical
            description: options.description || name
        });
    }

    /**
     * Run a single health check
     */
    async runCheck(checkName) {
        const check = this.checks.get(checkName);
        if (!check) {
            throw new Error(`Health check '${checkName}' not found`);
        }

        const startTime = Date.now();
        let result;

        try {
            // Run check with timeout
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error("Health check timeout")), check.timeout);
            });

            const checkResult = await Promise.race([
                check.checkFunction(),
                timeoutPromise
            ]);

            result = {
                name: checkName,
                status: "healthy",
                responseTime: Date.now() - startTime,
                timestamp: new Date().toISOString(),
                details: checkResult || {},
                critical: check.critical,
                description: check.description
            };
        } catch (error) {
            result = {
                name: checkName,
                status: "unhealthy",
                responseTime: Date.now() - startTime,
                timestamp: new Date().toISOString(),
                error: error.message,
                critical: check.critical,
                description: check.description
            };
        }

        this.lastCheckResults.set(checkName, result);
        return result;
    }

    /**
     * Run all health checks
     */
    async runAllChecks() {
        const results = {};
        const promises = [];

        for (const checkName of this.checks.keys()) {
            promises.push(
                this.runCheck(checkName).then(result => {
                    results[checkName] = result;
                }).catch(error => {
                    results[checkName] = {
                        name: checkName,
                        status: "error",
                        error: error.message,
                        timestamp: new Date().toISOString()
                    };
                })
            );
        }

        await Promise.all(promises);
        return results;
    }

    /**
     * Get overall health status
     */
    async getHealthStatus() {
        const checks = await this.runAllChecks();
        const circuitBreakerStatus = circuitBreakerManager.getHealthSummary();

        let overallStatus = "healthy";
        let criticalIssues = 0;
        let totalIssues = 0;

        for (const check of Object.values(checks)) {
            if (check.status !== "healthy") {
                totalIssues += 1;
                if (check.critical) {
                    criticalIssues += 1;
                    overallStatus = "unhealthy";
                }
            }
        }

        // Factor in circuit breaker status
        if (circuitBreakerStatus.openCircuits > 0) {
            overallStatus = "degraded";
        }

        if (criticalIssues === 0 && totalIssues > 0) {
            overallStatus = "degraded";
        }

        return {
            status: overallStatus,
            timestamp: new Date().toISOString(),
            summary: {
                totalChecks: Object.keys(checks).length,
                healthyChecks: Object.values(checks).filter(c => c.status === "healthy").length,
                unhealthyChecks: totalIssues,
                criticalIssues
            },
            circuitBreakers: circuitBreakerStatus,
            checks
        };
    }

    /**
     * Start periodic health checks
     */
    startPeriodicChecks() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
        }

        this.intervalId = setInterval(async () => {
            try {
                await this.runAllChecks();
                console.log("Periodic health checks completed");
            } catch (error) {
                console.error("Error during periodic health checks:", error);
            }
        }, this.checkInterval);

        console.log(`Started periodic health checks every ${this.checkInterval}ms`);
    }

    /**
     * Stop periodic health checks
     */
    stopPeriodicChecks() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
            console.log("Stopped periodic health checks");
        }
    }

    /**
     * Get last check results without running new checks
     */
    getLastResults() {
        const results = {};
        for (const [name, result] of this.lastCheckResults) {
            results[name] = result;
        }
        return results;
    }
}

/**
 * Pre-configured health checks for IAP services
 */
class IAPHealthChecks {
    static async googlePlayAPI() {
        const googleValidator = require("../services/googlePlayValidator");

        try {
            // Test Google Play API connectivity
            if (!googleValidator.androidPublisher) {
                await googleValidator.initializeAuth();
            }

            return {
                service: "Google Play API",
                authenticated: !!googleValidator.androidPublisher,
                message: "Google Play API connection successful"
            };
        } catch (error) {
            throw new Error(`Google Play API check failed: ${error.message}`);
        }
    }

    static async database() {
        const mongoose = require("mongoose");

        try {
            const state = mongoose.connection.readyState;
            const states = {
                0: "disconnected",
                1: "connected",
                2: "connecting",
                3: "disconnecting"
            };

            if (state !== 1) {
                throw new Error(`Database not connected. State: ${states[state]}`);
            }

            // Test a simple query
            const Purchase = require("../DB/schemes/iap/purchase");
            await Purchase.countDocuments().limit(1);

            return {
                service: "MongoDB",
                state: states[state],
                message: "Database connection healthy"
            };
        } catch (error) {
            throw new Error(`Database check failed: ${error.message}`);
        }
    }

    static async pubSubConnectivity() {
        try {
            const { PubSub } = require("@google-cloud/pubsub");
            const path = require("path");

            const credentialsPath = process.env.GOOGLE_SERVICE_ACCOUNT_KEY_PATH ||
                path.join(__dirname, "../../credentials.json");

            const pubsub = new PubSub({
                keyFilename: credentialsPath
            });

            const topicName = process.env.GOOGLE_IAP_TOPIC ||
                "projects/friendlychatclient-311211/topics/iap_notifications";

            const [topic] = await pubsub.topic(topicName.split("/").pop()).get();

            return {
                service: "Google Pub/Sub",
                topic: topicName,
                exists: !!topic,
                message: "Pub/Sub connectivity verified"
            };
        } catch (error) {
            throw new Error(`Pub/Sub check failed: ${error.message}`);
        }
    }

    static async memoryUsage() {
        const used = process.memoryUsage();
        const totalMB = Math.round(used.rss / 1024 / 1024);
        const heapUsedMB = Math.round(used.heapUsed / 1024 / 1024);
        const heapTotalMB = Math.round(used.heapTotal / 1024 / 1024);

        // Alert if memory usage is too high
        if (totalMB > 512) { // 512MB threshold
            throw new Error(`High memory usage: ${totalMB}MB`);
        }

        return {
            service: "Memory Usage",
            totalMB,
            heapUsedMB,
            heapTotalMB,
            message: `Memory usage normal: ${totalMB}MB`
        };
    }
}

// Global health check manager instance
const healthCheckManager = new HealthCheckManager();

// Register default IAP health checks
healthCheckManager.registerCheck("googlePlayAPI", IAPHealthChecks.googlePlayAPI, {
    description: "Google Play API connectivity and authentication",
    critical: true,
    timeout: 10000
});

healthCheckManager.registerCheck("database", IAPHealthChecks.database, {
    description: "MongoDB database connectivity",
    critical: true,
    timeout: 5000
});

healthCheckManager.registerCheck("pubSub", IAPHealthChecks.pubSubConnectivity, {
    description: "Google Pub/Sub connectivity for webhooks",
    critical: false,
    timeout: 8000
});

healthCheckManager.registerCheck("memory", IAPHealthChecks.memoryUsage, {
    description: "System memory usage monitoring",
    critical: false,
    timeout: 1000
});

module.exports = {
    HealthCheckManager,
    IAPHealthChecks,
    healthCheckManager
};
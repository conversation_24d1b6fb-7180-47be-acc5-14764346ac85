const IAPErrorHandler = require("../middleware/errorHandler");
const { circuitBreakerManager } = require("./circuitBreakerManager");
const HealthCheckManager = require("./healthCheckManager");

/**
 * IAP System Initializer
 * Sets up all error handling, monitoring, and fault tolerance systems
 */
class IAPInitializer {
    constructor() {
        this.healthCheckManager = new HealthCheckManager();
        this.initialized = false;
    }

    /**
     * Initialize all IAP fault tolerance systems
     */
    async initialize() {
        if (this.initialized) {
            console.log("IAP systems already initialized");
            return;
        }

        console.log("Initializing IAP fault tolerance systems...");

        try {
            // Initialize global error handlers
            this.initializeErrorHandlers();

            // Initialize circuit breakers
            this.initializeCircuitBreakers();

            // Initialize health checks
            await this.initializeHealthChecks();

            // Start periodic monitoring
            this.startMonitoring();

            this.initialized = true;
            console.log("✅ IAP fault tolerance systems initialized successfully");
        } catch (error) {
            console.error("❌ Failed to initialize IAP systems:", error);
            throw error;
        }
    }

    /**
     * Initialize global error handlers
     */
    initializeErrorHandlers() {
        console.log("Setting up global error handlers...");
        IAPErrorHandler.initialize();
    }

    /**
     * Initialize circuit breakers for different services
     */
    initializeCircuitBreakers() {
        console.log("Configuring circuit breakers...");

        // Google Play API circuit breaker
        circuitBreakerManager.getBreaker("google-play-api", {
            failureThreshold: 5,
            resetTimeout: 60000, // 1 minute
            monitoringPeriod: 30000 // 30 seconds
        });

        // Apple App Store API circuit breaker
        circuitBreakerManager.getBreaker("apple-app-store-api", {
            failureThreshold: 3,
            resetTimeout: 45000, // 45 seconds
            monitoringPeriod: 20000 // 20 seconds
        });

        // Database operations circuit breaker
        circuitBreakerManager.getBreaker("database", {
            failureThreshold: 3,
            resetTimeout: 30000, // 30 seconds
            monitoringPeriod: 15000 // 15 seconds
        });

        // Webhook processing circuit breakers
        circuitBreakerManager.getBreaker("google-webhook", {
            failureThreshold: 2,
            resetTimeout: 60000, // 1 minute
            monitoringPeriod: 30000 // 30 seconds
        });

        circuitBreakerManager.getBreaker("apple-webhook", {
            failureThreshold: 2,
            resetTimeout: 60000, // 1 minute
            monitoringPeriod: 30000 // 30 seconds
        });

        console.log("Circuit breakers configured");
    }

    /**
     * Initialize health checks
     */
    async initializeHealthChecks() {
        console.log("Setting up health checks...");

        // Google Play API health check
        this.healthCheckManager.registerCheck("googlePlayAPI", async () => {
            const googleValidator = require("../services/googlePlayValidator");

            if (!googleValidator.androidPublisher) {
                await googleValidator.initializeAuth();
            }

            return {
                service: "Google Play API",
                authenticated: Boolean(googleValidator.androidPublisher),
                message: "Google Play API connection successful"
            };
        }, {
            description: "Google Play API connectivity and authentication",
            critical: true,
            timeout: 10000
        });

        // Database health check
        this.healthCheckManager.registerCheck("database", async () => {
            const mongoose = require("mongoose");

            const state = mongoose.connection.readyState;
            const states = {
                0: "disconnected",
                1: "connected",
                2: "connecting",
                3: "disconnecting"
            };

            if (state !== 1) {
                throw new Error(`Database not connected. State: ${states[state]}`);
            }

            // Test a simple query
            const Purchase = require("../DB/schemes/iap/purchase");
            await Purchase.countDocuments().limit(1);

            return {
                service: "MongoDB",
                state: states[state],
                message: "Database connection healthy"
            };
        }, {
            description: "MongoDB database connectivity",
            critical: true,
            timeout: 5000
        });

        // Memory usage health check
        this.healthCheckManager.registerCheck("memory", async () => {
            const used = process.memoryUsage();
            const totalMB = Math.round(used.rss / 1024 / 1024);
            const heapUsedMB = Math.round(used.heapUsed / 1024 / 1024);
            const heapTotalMB = Math.round(used.heapTotal / 1024 / 1024);

            // Alert if memory usage is too high
            if (totalMB > 512) { // 512MB threshold
                throw new Error(`High memory usage: ${totalMB}MB`);
            }

            return {
                service: "Memory Usage",
                totalMB,
                heapUsedMB,
                heapTotalMB,
                message: `Memory usage normal: ${totalMB}MB`
            };
        }, {
            description: "System memory usage monitoring",
            critical: false,
            timeout: 1000
        });

        console.log("Health checks configured");
    }

    /**
     * Start periodic monitoring
     */
    startMonitoring() {
        console.log("Starting periodic monitoring...");

        // Start health check monitoring
        this.healthCheckManager.startPeriodicChecks();

        // Log circuit breaker status periodically
        setInterval(() => {
            const summary = circuitBreakerManager.getHealthSummary();
            if (summary.openCircuits > 0 || summary.unhealthyServices > 0) {
                console.warn("Circuit Breaker Alert:", summary);
            }
        }, 60000); // Every minute

        console.log("Periodic monitoring started");
    }

    /**
     * Get system status
     */
    async getSystemStatus() {
        const healthStatus = await this.healthCheckManager.getHealthStatus();
        const circuitBreakerStatus = circuitBreakerManager.getHealthSummary();

        return {
            initialized: this.initialized,
            timestamp: new Date().toISOString(),
            health: healthStatus,
            circuitBreakers: circuitBreakerStatus,
            overallStatus: this.calculateOverallStatus(healthStatus, circuitBreakerStatus)
        };
    }

    /**
     * Calculate overall system status
     */
    calculateOverallStatus(healthStatus, circuitBreakerStatus) {
        if (healthStatus.status === "unhealthy" || circuitBreakerStatus.openCircuits > 2) {
            return "critical";
        }

        if (healthStatus.status === "degraded" || circuitBreakerStatus.openCircuits > 0) {
            return "degraded";
        }

        return "healthy";
    }

    /**
     * Shutdown gracefully
     */
    async shutdown() {
        console.log("Shutting down IAP systems...");

        this.healthCheckManager.stopPeriodicChecks();
        circuitBreakerManager.resetAll();

        console.log("IAP systems shutdown complete");
    }
}

// Global initializer instance
const iapInitializer = new IAPInitializer();

module.exports = {
    IAPInitializer,
    iapInitializer
};
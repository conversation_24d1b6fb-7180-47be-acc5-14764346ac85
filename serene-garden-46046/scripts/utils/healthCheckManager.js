const { circuitBreakerManager } = require("./circuitBreakerManager");

/**
 * Health check system for IAP dependencies
 * Monitors external services and database connectivity
 */
class HealthCheckManager {
    constructor() {
        this.checks = new Map();
        this.lastCheckResults = new Map();
        this.checkInterval = 30000; // 30 seconds
        this.intervalId = null;
    }

    /**
     * Register a health check
     */
    registerCheck(name, checkFunction, options = {}) {
        this.checks.set(name, {
            name,
            checkFunction,
            timeout: options.timeout || 5000,
            critical: options.critical !== false, // Default to critical
            description: options.description || name
        });
    }

    /**
     * Run a single health check
     */
    async runCheck(checkName) {
        const check = this.checks.get(checkName);
        if (!check) {
            throw new Error(`Health check '${checkName}' not found`);
        }

        const startTime = Date.now();

        try {
            const checkResult = await this.executeCheckWithTimeout(check);
            return this.createHealthyResult(checkName, startTime, checkResult, check);
        } catch (error) {
            return this.createUnhealthyResult(checkName, startTime, error, check);
        }
    }

    /**
     * Execute check with timeout
     */
    async executeCheckWithTimeout(check) {
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error("Health check timeout")), check.timeout);
        });

        return Promise.race([
            check.checkFunction(),
            timeoutPromise
        ]);
    }

    /**
     * Create healthy result object
     */
    createHealthyResult(checkName, startTime, checkResult, check) {
        const result = {
            name: checkName,
            status: "healthy",
            responseTime: Date.now() - startTime,
            timestamp: new Date().toISOString(),
            details: checkResult || {},
            critical: check.critical,
            description: check.description
        };

        this.lastCheckResults.set(checkName, result);
        return result;
    }

    /**
     * Create unhealthy result object
     */
    createUnhealthyResult(checkName, startTime, error, check) {
        const result = {
            name: checkName,
            status: "unhealthy",
            responseTime: Date.now() - startTime,
            timestamp: new Date().toISOString(),
            error: error.message,
            critical: check.critical,
            description: check.description
        };

        this.lastCheckResults.set(checkName, result);
        return result;
    }

    /**
     * Run all health checks
     */
    async runAllChecks() {
        const results = {};
        const promises = [];

        for (const checkName of this.checks.keys()) {
            promises.push(
                this.runCheck(checkName).then((result) => {
                    results[checkName] = result;
                }).catch((error) => {
                    results[checkName] = {
                        name: checkName,
                        status: "error",
                        error: error.message,
                        timestamp: new Date().toISOString()
                    };
                })
            );
        }

        await Promise.all(promises);
        return results;
    }

    /**
     * Get overall health status
     */
    async getHealthStatus() {
        const checks = await this.runAllChecks();
        const circuitBreakerStatus = circuitBreakerManager.getHealthSummary();

        const statusSummary = this.calculateOverallStatus(checks, circuitBreakerStatus);

        return {
            status: statusSummary.overallStatus,
            timestamp: new Date().toISOString(),
            summary: {
                totalChecks: Object.keys(checks).length,
                healthyChecks: Object.values(checks).filter((c) => c.status === "healthy").length,
                unhealthyChecks: statusSummary.totalIssues,
                criticalIssues: statusSummary.criticalIssues
            },
            circuitBreakers: circuitBreakerStatus,
            checks
        };
    }

    /**
     * Calculate overall status from checks
     */
    calculateOverallStatus(checks, circuitBreakerStatus) {
        let overallStatus = "healthy";
        let criticalIssues = 0;
        let totalIssues = 0;

        for (const check of Object.values(checks)) {
            if (check.status !== "healthy") {
                totalIssues += 1;
                if (check.critical) {
                    criticalIssues += 1;
                    overallStatus = "unhealthy";
                }
            }
        }

        // Factor in circuit breaker status
        if (circuitBreakerStatus.openCircuits > 0) {
            overallStatus = "degraded";
        }

        if (criticalIssues === 0 && totalIssues > 0) {
            overallStatus = "degraded";
        }

        return { overallStatus, criticalIssues, totalIssues };
    }

    /**
     * Start periodic health checks
     */
    startPeriodicChecks() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
        }

        this.intervalId = setInterval(async () => {
            try {
                await this.runAllChecks();
                console.log("Periodic health checks completed");
            } catch (error) {
                console.error("Error during periodic health checks:", error);
            }
        }, this.checkInterval);

        console.log(`Started periodic health checks every ${this.checkInterval}ms`);
    }

    /**
     * Stop periodic health checks
     */
    stopPeriodicChecks() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
            console.log("Stopped periodic health checks");
        }
    }

    /**
     * Get last check results without running new checks
     */
    getLastResults() {
        const results = {};
        for (const [name, result] of this.lastCheckResults) {
            results[name] = result;
        }
        return results;
    }
}

module.exports = HealthCheckManager;
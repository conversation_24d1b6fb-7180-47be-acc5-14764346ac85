/**
 * Retry utility for external API calls with exponential backoff
 * Prevents cascading failures and improves resilience
 */
class RetryManager {
    /**
     * Execute a function with retry logic
     * @param {Function} fn - Function to execute
     * @param {Object} options - Retry options
     * @param {number} options.maxRetries - Maximum number of retries (default: 3)
     * @param {number} options.initialDelay - Initial delay in ms (default: 1000)
     * @param {number} options.maxDelay - Maximum delay in ms (default: 10000)
     * @param {Function} options.shouldRetry - Function to determine if retry should occur
     * @param {Function} options.onRetry - Callback on each retry attempt
     * @returns {Promise} Result of the function
     */
    static async withRetry(fn, options = {}) {
        const {
            maxRetries = 3,
            initialDelay = 1000,
            maxDelay = 10000,
            shouldRetry = this.defaultShouldRetry,
            onRetry = () => { }
        } = options;

        let lastError;

        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                return await fn();
            } catch (error) {
                lastError = error;

                // Check if we should retry
                if (attempt === maxRetries || !shouldRetry(error)) {
                    break;
                }

                // Calculate delay with exponential backoff and jitter
                const delay = Math.min(
                    initialDelay * Math.pow(2, attempt) + Math.random() * 1000,
                    maxDelay
                );

                onRetry(attempt + 1, delay, error);

                // Wait before retrying
                await this.delay(delay);
            }
        }

        throw lastError;
    }

    /**
     * Default retry condition - retry on network errors and 5xx status codes
     */
    static defaultShouldRetry(error) {
        // Network errors
        if (error.code === "ECONNREFUSED" || error.code === "ETIMEDOUT") {
            return true;
        }

        // HTTP 5xx errors
        if (error.response && error.response.status >= 500 && error.response.status < 600) {
            return true;
        }

        // Google API quota errors
        if (error.message && error.message.includes("quota")) {
            return true;
        }

        return false;
    }

    /**
     * Delay execution
     */
    static delay(ms) {
        return new Promise((resolve) => {
            setTimeout(resolve, ms);
        });
    }

    /**
     * Create a retry wrapper for Google Play API calls
     */
    static forGooglePlayAPI(fn) {
        return this.withRetry(fn, {
            maxRetries: 5,
            initialDelay: 2000,
            maxDelay: 30000,
            shouldRetry: (error) => {
                // Retry on Google API rate limits and quota errors
                if (error.code === 429 || error.code === 503) {
                    return true;
                }

                // Retry on network issues
                if (error.code && ["ECONNREFUSED", "ETIMEDOUT", "ENOTFOUND"].includes(error.code)) {
                    return true;
                }

                return this.defaultShouldRetry(error);
            },
            onRetry: (attempt, delay, error) => {
                console.warn(`Google Play API retry attempt ${attempt} in ${delay}ms:`, error.message);
            }
        });
    }

    /**
     * Create a retry wrapper for Apple App Store API calls
     */
    static forAppleAppStore(fn) {
        return this.withRetry(fn, {
            maxRetries: 3,
            initialDelay: 1000,
            maxDelay: 10000,
            shouldRetry: (error) => {
                // Apple App Store specific retry conditions
                if (error.statusCode && error.statusCode >= 500) {
                    return true;
                }

                // Network issues
                if (error.code && ["ECONNREFUSED", "ETIMEDOUT"].includes(error.code)) {
                    return true;
                }

                return false;
            },
            onRetry: (attempt, delay, error) => {
                console.warn(`Apple App Store API retry attempt ${attempt} in ${delay}ms:`, error.message);
            }
        });
    }

    /**
     * Create a retry wrapper for database operations
     */
    static forDatabase(fn) {
        return this.withRetry(fn, {
            maxRetries: 2,
            initialDelay: 500,
            maxDelay: 2000,
            shouldRetry: (error) => {
                // MongoDB connection errors
                if (error.name === "MongoNetworkError" || error.code === "ECONNREFUSED") {
                    return true;
                }

                // MongoDB timeout errors
                if (error.code === "ETIMEDOUT") {
                    return true;
                }

                return false;
            },
            onRetry: (attempt, delay, error) => {
                console.warn(`Database operation retry attempt ${attempt} in ${delay}ms:`, error.message);
            }
        });
    }
}

module.exports = RetryManager;

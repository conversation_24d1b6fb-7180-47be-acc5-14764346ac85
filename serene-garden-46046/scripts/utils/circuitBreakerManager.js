const CircuitBreaker = require("./circuitBreaker");

/**
 * Circuit Breaker Manager for managing multiple circuit breakers
 */
class CircuitBreakerManager {
    constructor() {
        this.breakers = new Map();
    }

    /**
     * Create or get a circuit breaker for a service
     */
    getBreaker(serviceName, options = {}) {
        if (!this.breakers.has(serviceName)) {
            const breakerOptions = {
                name: serviceName,
                ...options
            };
            this.breakers.set(serviceName, new CircuitBreaker(breakerOptions));
        }
        return this.breakers.get(serviceName);
    }

    /**
     * Execute function with circuit breaker protection
     */
    execute(serviceName, fn, options = {}) {
        const breaker = this.getBreaker(serviceName, options);
        return breaker.execute(fn);
    }

    /**
     * Get status of all circuit breakers
     */
    getAllStatus() {
        const status = {};
        for (const [name, breaker] of this.breakers) {
            status[name] = breaker.getStatus();
        }
        return status;
    }

    /**
     * Get health summary
     */
    getHealthSummary() {
        const summary = {
            totalServices: this.breakers.size,
            healthyServices: 0,
            unhealthyServices: 0,
            openCircuits: 0
        };

        for (const breaker of this.breakers.values()) {
            if (breaker.isHealthy()) {
                summary.healthyServices += 1;
            } else {
                summary.unhealthyServices += 1;
            }

            if (breaker.state === "OPEN") {
                summary.openCircuits += 1;
            }
        }

        return summary;
    }

    /**
     * Reset all circuit breakers
     */
    resetAll() {
        for (const breaker of this.breakers.values()) {
            breaker.reset();
        }
    }
}

// Global circuit breaker manager instance
const circuitBreakerManager = new CircuitBreakerManager();

module.exports = {
    CircuitBreakerManager,
    circuitBreakerManager
};
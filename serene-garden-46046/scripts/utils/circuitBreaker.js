/**
 * Circuit Breaker pattern implementation for external services
 * Prevents cascading failures by temporarily disabling failing services
 */
class CircuitBreaker {
    constructor(options = {}) {
        this.failureThreshold = options.failureThreshold || 5;
        this.resetTimeout = options.resetTimeout || 60000; // 1 minute
        this.monitoringPeriod = options.monitoringPeriod || 10000; // 10 seconds
        this.name = options.name || "CircuitBreaker";

        this.state = "CLOSED"; // CLOSED, OPEN, HALF_OPEN
        this.failureCount = 0;
        this.lastFailureTime = null;
        this.successCount = 0;
        this.requestCount = 0;

        // Statistics
        this.stats = {
            totalRequests: 0,
            totalFailures: 0,
            totalSuccesses: 0,
            lastReset: new Date()
        };
    }

    /**
     * Execute a function with circuit breaker protection
     */
    async execute(fn) {
        this.stats.totalRequests += 1;
        this.requestCount += 1;

        if (this.state === "OPEN") {
            this.handleOpenState();
        }

        // eslint-disable-next-line no-return-await
        return await this.executeFunction(fn);
    }

    /**
     * Handle circuit breaker in OPEN state
     */
    handleOpenState() {
        if (this.shouldAttemptReset()) {
            this.state = "HALF_OPEN";
            console.log(`[${this.name}] Circuit breaker transitioning to HALF_OPEN`);
        } else {
            const error = new Error(`Circuit breaker is OPEN for ${this.name}`);
            error.code = "CIRCUIT_BREAKER_OPEN";
            throw error;
        }
    }

    /**
     * Execute the function and handle results
     */
    async executeFunction(fn) {
        try {
            const result = await fn();
            this.onSuccess();
            return result;
        } catch (error) {
            this.onFailure();
            throw error;
        }
    }

    /**
     * Handle successful execution
     */
    onSuccess() {
        this.failureCount = 0;
        this.successCount += 1;
        this.stats.totalSuccesses += 1;

        if (this.state === "HALF_OPEN") {
            this.state = "CLOSED";
            console.log(`[${this.name}] Circuit breaker reset to CLOSED`);
            this.stats.lastReset = new Date();
        }
    }

    /**
     * Handle failed execution
     */
    onFailure() {
        this.failureCount += 1;
        this.stats.totalFailures += 1;
        this.lastFailureTime = new Date();

        if (this.failureCount >= this.failureThreshold) {
            this.state = "OPEN";
            console.warn(`[${this.name}] Circuit breaker OPENED after ${this.failureCount} failures`);
        }
    }

    /**
     * Check if we should attempt to reset the circuit breaker
     */
    shouldAttemptReset() {
        return this.lastFailureTime &&
            Date.now() - this.lastFailureTime.getTime() >= this.resetTimeout;
    }

    /**
     * Get current circuit breaker status
     */
    getStatus() {
        return {
            name: this.name,
            state: this.state,
            failureCount: this.failureCount,
            successCount: this.successCount,
            requestCount: this.requestCount,
            lastFailureTime: this.lastFailureTime,
            stats: this.stats,
            healthPercentage: this.getHealthPercentage()
        };
    }

    /**
     * Calculate health percentage based on recent requests
     */
    getHealthPercentage() {
        if (this.requestCount === 0) return 100;
        return Math.round(this.successCount / this.requestCount * 100);
    }

    /**
     * Reset circuit breaker manually
     */
    reset() {
        this.state = "CLOSED";
        this.failureCount = 0;
        this.successCount = 0;
        this.requestCount = 0;
        this.lastFailureTime = null;
        this.stats.lastReset = new Date();
        console.log(`[${this.name}] Circuit breaker manually reset`);
    }

    /**
     * Check if circuit breaker is healthy
     */
    isHealthy() {
        return this.state === "CLOSED" && this.getHealthPercentage() > 50;
    }
}

module.exports = CircuitBreaker;

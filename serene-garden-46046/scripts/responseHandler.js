const socket = require("../app.socket.io");

module.exports.handleMessage = (user, received_message) => {
  let response;

  // Checks if the message contains text
  if (received_message.text) {
    // Create the payload for a basic text message, which
    // will be added to the body of our request to the Send API
    response = received_message;
  } else if (received_message.attachments) {
    let attachment_url = received_message.attachments[0].payload.url;
    let type = received_message.attachments[0].type;
    response = {
      source: received_message.source,
      attachment: {
        type: type,
        payload: {
          url: attachment_url,
        },
      },
    };
  }

  // Send the response message
  socket.sendMessageToUser(user.sessionId, response || received_message);
};

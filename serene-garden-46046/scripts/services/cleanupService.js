const iapService = require('./iapService');
const cron = require('node-cron');

class CleanupService {
  constructor() {
    this.isRunning = false;
    this.cleanupInterval = process.env.IAP_CLEANUP_INTERVAL || 3600000; // 1 hour default
  }

  /**
   * Start the cleanup service with periodic execution
   */
  start() {
    if (this.isRunning) {
      console.log('Cleanup service is already running');
      return;
    }

    console.log('Starting IAP cleanup service...');
    this.isRunning = true;

    // Run cleanup every hour (0 * * * * - at minute 0 of every hour)
    this.cronJob = cron.schedule('0 * * * *', async () => {
      try {
        console.log('Running scheduled IAP cleanup...');
        await this.runCleanup();
      } catch (error) {
        console.error('Scheduled cleanup failed:', error);
      }
    }, {
      scheduled: true,
      timezone: "UTC"
    });

    // Run initial cleanup
    setTimeout(async () => {
      try {
        console.log('Running initial IAP cleanup...');
        await this.runCleanup();
      } catch (error) {
        console.error('Initial cleanup failed:', error);
      }
    }, 5000); // Wait 5 seconds after startup

    console.log('IAP cleanup service started successfully');
  }

  /**
   * Stop the cleanup service
   */
  stop() {
    if (!this.isRunning) {
      console.log('Cleanup service is not running');
      return;
    }

    console.log('Stopping IAP cleanup service...');
    
    if (this.cronJob) {
      this.cronJob.destroy();
      this.cronJob = null;
    }

    this.isRunning = false;
    console.log('IAP cleanup service stopped');
  }

  /**
   * Run the cleanup process
   */
  async runCleanup() {
    try {
      const startTime = Date.now();
      console.log('Starting IAP cleanup process...');

      // Clean up expired purchases
      const cleanupResult = await iapService.cleanupExpiredPurchases();
      
      const endTime = Date.now();
      const duration = endTime - startTime;

      console.log(`IAP cleanup completed in ${duration}ms:`, {
        expiredPurchasesUpdated: cleanupResult.modifiedCount,
        timestamp: new Date().toISOString()
      });

      return {
        success: true,
        duration,
        expiredPurchasesUpdated: cleanupResult.modifiedCount
      };

    } catch (error) {
      console.error('IAP cleanup process failed:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get cleanup service status
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      cleanupInterval: this.cleanupInterval,
      nextRun: this.cronJob ? this.cronJob.nextDate() : null,
      lastRun: this.lastRunTime || null
    };
  }

  /**
   * Run cleanup manually (for testing or admin purposes)
   */
  async runManualCleanup() {
    try {
      console.log('Running manual IAP cleanup...');
      const result = await this.runCleanup();
      this.lastRunTime = new Date();
      return result;
    } catch (error) {
      console.error('Manual cleanup failed:', error);
      throw error;
    }
  }
}

// Create singleton instance
const cleanupService = new CleanupService();

// Graceful shutdown handling
process.on('SIGINT', () => {
  console.log('Received SIGINT, stopping cleanup service...');
  cleanupService.stop();
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('Received SIGTERM, stopping cleanup service...');
  cleanupService.stop();
  process.exit(0);
});

module.exports = cleanupService;

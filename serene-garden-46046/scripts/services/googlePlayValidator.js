/* eslint-disable class-methods-use-this */
/* eslint-disable max-statements */
/* eslint-disable max-lines-per-function */
const { google } = require("googleapis");
const path = require("path");
const RetryManager = require("../utils/retry");
const { circuitBreakerManager } = require("../utils/circuitBreakerManager");

class GooglePlayValidator {
  constructor() {
    this.androidPublisher = null;
    this.initializeAuth();
  }

  /**
   * Initialize Google Play API authentication
   */
  async initializeAuth() {
    try {
      // Load service account credentials
      const credentialsPath = process.env.GOOGLE_SERVICE_ACCOUNT_KEY_PATH ||
        path.join(__dirname, "../../credentials.json");

      const auth = new google.auth.GoogleAuth({
        keyFile: credentialsPath,
        scopes: ["https://www.googleapis.com/auth/androidpublisher"]
      });

      this.androidPublisher = await google.androidpublisher({
        version: "v3",
        auth: auth
      });

      console.log("Google Play API authentication initialized");
    } catch (error) {
      console.error("Failed to initialize Google Play API auth:", error);
      throw error;
    }
  }

  /**
   * Validate a Google Play purchase
   * @param {string} productId - Product ID
   * @param {string} purchaseToken - Purchase token from the receipt
   * @param {string} packageName - Package name from the receipt
   * @returns {Promise<Object>} Validation result
   */
  async validatePurchase(productId, purchaseToken, packageName) {
    try {
      console.log(`Validating Google Play purchase for product: ${productId}, package: ${packageName}`);

      if (!this.androidPublisher) {
        await this.initializeAuth();
      }

      if (!packageName) {
        throw new Error("Package name is required for Google Play validation");
      }

      // Use circuit breaker and retry for Google Play API call
      const response = await circuitBreakerManager.execute("google-play-api", async () => {
        return RetryManager.forGooglePlayAPI(async () => {
          return this.androidPublisher.purchases.products.get({
            packageName: packageName,
            productId: productId,
            token: purchaseToken
          });
        });
      });

      const purchase = response.data;

      // Parse purchase information
      const purchaseInfo = {
        transactionId: purchase.orderId,
        originalTransactionId: purchase.orderId,
        productId: productId,
        purchaseDate: new Date(parseInt(purchase.purchaseTimeMillis, 10)),
        quantity: parseInt(purchase.quantity, 10) || 1,
        purchaseState: parseInt(purchase.purchaseState, 10), // 0: Purchased, 1: Canceled
        consumptionState: parseInt(purchase.consumptionState, 10), // 0: Yet to be consumed, 1: Consumed
        developerPayload: purchase.developerPayload,
        purchaseToken: purchaseToken,
        environment: "production", // Google Play doesn't distinguish sandbox in API response
        packageName: packageName
      };

      // Determine if purchase is active
      purchaseInfo.isActive = purchaseInfo.purchaseState === 0; // 0 = Purchased
      purchaseInfo.isExpired = purchaseInfo.purchaseState === 1; // 1 = Canceled
      purchaseInfo.isConsumed = purchaseInfo.consumptionState === 1;

      return {
        success: true,
        platform: "android",
        purchase: purchaseInfo,
        rawResponse: purchase
      };

    } catch (error) {
      console.error("Google Play purchase validation error:", error);
      return {
        success: false,
        platform: "android",
        error: error.message,
        errorCode: error.code || "VALIDATION_FAILED"
      };
    }
  }

  /**
   * Validate a Google Play subscription
   * @param {string} subscriptionId - Subscription product ID
   * @param {string} purchaseToken - Purchase token from the receipt
   * @param {string} packageName - Package name from the receipt
   * @returns {Promise<Object>} Subscription validation result
   */
  async validateSubscription(subscriptionId, purchaseToken, packageName) {
    try {
      console.log(`Validating Google Play subscription: ${subscriptionId}, package: ${packageName}`);

      if (!this.androidPublisher) {
        await this.initializeAuth();
      }

      if (!packageName) {
        throw new Error("Package name is required for Google Play subscription validation");
      }

      // Use circuit breaker and retry for Google Play API call
      const response = await circuitBreakerManager.execute("google-play-api", async () => {
        return RetryManager.forGooglePlayAPI(async () => {
          return this.androidPublisher.purchases.subscriptions.get({
            packageName: packageName,
            subscriptionId: subscriptionId,
            token: purchaseToken
          });
        });
      });

      const subscription = response.data;

      // Parse subscription information
      const subscriptionInfo = {
        transactionId: subscription.orderId,
        originalTransactionId: subscription.linkedPurchaseToken || subscription.orderId,
        productId: subscriptionId,
        purchaseDate: new Date(parseInt(subscription.startTimeMillis, 10)),
        expiryDate: new Date(parseInt(subscription.expiryTimeMillis, 10)),
        autoRenewing: subscription.autoRenewing,
        paymentState: parseInt(subscription.paymentState, 10), // 0: Payment pending, 1: Payment received
        cancelReason: subscription.cancelReason,
        userCancellationTimeMillis: subscription.userCancellationTimeMillis,
        purchaseToken: purchaseToken,
        environment: "production",
        packageName: packageName,
        countryCode: subscription.countryCode,
        priceAmountMicros: subscription.priceAmountMicros,
        priceCurrencyCode: subscription.priceCurrencyCode
      };

      // Determine subscription status
      const now = new Date();
      subscriptionInfo.isActive = subscriptionInfo.expiryDate > now &&
        subscriptionInfo.paymentState === 1;
      subscriptionInfo.isExpired = subscriptionInfo.expiryDate <= now;
      subscriptionInfo.willRenew = subscriptionInfo.autoRenewing && subscriptionInfo.isActive;

      // Handle cancellation information
      if (subscription.userCancellationTimeMillis) {
        subscriptionInfo.cancellationDate = new Date(parseInt(subscription.userCancellationTimeMillis, 10));
        subscriptionInfo.willRenew = false;
      }

      return {
        success: true,
        platform: "android",
        purchase: subscriptionInfo,
        rawResponse: subscription
      };

    } catch (error) {
      console.error("Google Play subscription validation error:", error);
      return {
        success: false,
        platform: "android",
        error: error.message,
        errorCode: error.code || "SUBSCRIPTION_VALIDATION_FAILED"
      };
    }
  }

  /**
   * Acknowledge a purchase (required for new purchases)
   * @param {string} productId - Product ID
   * @param {string} purchaseToken - Purchase token
   * @param {string} packageName - Package name from the receipt
   * @returns {Promise<boolean>} Success status
   */
  async acknowledgePurchase(productId, purchaseToken, packageName) {
    try {
      if (!packageName) {
        throw new Error("Package name is required for purchase acknowledgment");
      }

      if (!this.androidPublisher) {
        await this.initializeAuth();
      }

      // Use circuit breaker and retry for acknowledgment
      await circuitBreakerManager.execute("google-play-api", async () => {
        return RetryManager.forGooglePlayAPI(async () => {
          return this.androidPublisher.purchases.products.acknowledge({
            packageName: packageName,
            productId: productId,
            token: purchaseToken
          });
        });
      });

      console.log(`Purchase acknowledged: ${productId}`);
      return true;
    } catch (error) {
      console.error("Error acknowledging purchase:", error);
      return false;
    }
  }

  /**
   * Parse Google Play Developer Notification
   * @param {Object} notificationPayload - Notification payload from Google
   * @returns {Object} Parsed notification data
   */
  parseServerNotification(notificationPayload) {
    try {
      const message = notificationPayload.message;
      const data = JSON.parse(Buffer.from(message.data, "base64").toString());

      let parsedNotification = {
        version: data.version,
        packageName: data.packageName,
        eventTimeMillis: data.eventTimeMillis,
        subscriptionNotification: data.subscriptionNotification,
        oneTimeProductNotification: data.oneTimeProductNotification,
        testNotification: data.testNotification
      };

      // Process subscription notifications
      if (data.subscriptionNotification) {
        const subNotification = this.handleSubscriptionNotification(data.subscriptionNotification);
        parsedNotification = {
          ...parsedNotification,
          type: subNotification.type,
          transactionId: data.subscriptionNotification.purchaseToken, // Use purchaseToken as transaction ID
          originalTransactionId: data.subscriptionNotification.purchaseToken,
          productId: subNotification.subscriptionId
        };
      }

      return parsedNotification;
    } catch (error) {
      console.error("Error parsing Google Play notification:", error);
      throw error;
    }
  }

  /**
   * Handle subscription notification
   * @param {Object} subscriptionNotification - Subscription notification data
   * @returns {Object} Processed notification info
   */
  handleSubscriptionNotification(subscriptionNotification) {
    const notificationTypes = {
      1: "SUBSCRIPTION_RECOVERED",
      2: "SUBSCRIPTION_RENEWED",
      3: "SUBSCRIPTION_CANCELED",
      4: "SUBSCRIPTION_PURCHASED",
      5: "SUBSCRIPTION_ON_HOLD",
      6: "SUBSCRIPTION_IN_GRACE_PERIOD",
      7: "SUBSCRIPTION_RESTARTED",
      8: "SUBSCRIPTION_PRICE_CHANGE_CONFIRMED",
      9: "SUBSCRIPTION_DEFERRED",
      10: "SUBSCRIPTION_PAUSED",
      11: "SUBSCRIPTION_PAUSE_SCHEDULE_CHANGED",
      12: "SUBSCRIPTION_REVOKED",
      13: "SUBSCRIPTION_EXPIRED"
    };

    return {
      type: notificationTypes[subscriptionNotification.notificationType] || "UNKNOWN",
      subscriptionId: subscriptionNotification.subscriptionId,
      purchaseToken: subscriptionNotification.purchaseToken,
      notificationType: subscriptionNotification.notificationType
    };
  }
}

module.exports = new GooglePlayValidator();

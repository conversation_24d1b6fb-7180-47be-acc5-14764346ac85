/* eslint-disable class-methods-use-this */
/* eslint-disable max-lines */
/* eslint-disable max-params */
/* eslint-disable max-lines-per-function */
/* eslint-disable max-statements */
const appleValidator = require("./appleReceiptValidator");
const googleValidator = require("./googlePlayValidator");
const PurchaseController = require("../DB/schemes/iap/purchase.ctrl");
const UserController = require("../DB/schemes/user/user.ctrl");

class IAPService {
  constructor() {
    this.supportedProducts = {
      "ad_free_subscription": {
        type: "subscription",
        platforms: ["ios", "android"]
      },
      "one_month_ad_free": {
        type: "consumable",
        platforms: ["ios", "android"],
        duration: 30 * 24 * 60 * 60 * 1000 // 30 days in milliseconds
      }
    };
  }

  /**
   * Verify a purchase receipt from either platform
   * @param {Object} receiptData - Receipt information
   * @returns {Promise<Object>} Verification result
   */
  async verifyPurchase(receiptData) {
    try {
      const { platform, productId, receipt, userId, deviceId } = receiptData;

      console.log(`Verifying ${platform} purchase for product: ${productId}`);

      // Validate input
      if (!this.supportedProducts[productId]) {
        throw new Error(`Unsupported product: ${productId}`);
      }

      let validationResult;

      // Validate based on platform
      if (platform === "ios") {
        if (this.supportedProducts[productId].type === "subscription") {
          validationResult = await appleValidator.validateSubscription(receipt, productId);
        } else {
          validationResult = await appleValidator.validateReceipt(receipt, productId);
        }
      } else if (platform === "android") {
        const { purchaseToken } = receiptData;
        if (!purchaseToken) {
          throw new Error("Purchase token required for Android validation");
        }

        // Extract packageName from Android receipt
        let packageName;
        try {
          const parsedReceipt = JSON.parse(receipt);
          packageName = parsedReceipt.packageName;
          console.log(`Extracted packageName from receipt: ${packageName}`);
        } catch (parseError) {
          console.error("Error parsing Android receipt for packageName:", parseError);
          throw new Error("Failed to parse Android receipt for packageName");
        }

        if (this.supportedProducts[productId].type === "subscription") {
          validationResult = await googleValidator.validateSubscription(productId, purchaseToken, packageName);
        } else {
          validationResult = await googleValidator.validatePurchase(productId, purchaseToken, packageName);
        }
      } else {
        throw new Error(`Unsupported platform: ${platform}`);
      }

      if (!validationResult.success) {
        return {
          success: false,
          error: validationResult.error,
          errorCode: validationResult.errorCode
        };
      }

      // Process the validated purchase
      const processResult = await this.processPurchase(validationResult, userId, deviceId, receipt);

      // CRITICAL: Acknowledge Google Play purchases immediately to prevent automatic refunds
      if (platform === "android" && validationResult.success) {
        await this.acknowledgeGooglePlayPurchase(receiptData, productId);
      }

      return {
        success: true,
        purchase: processResult.purchase,
        userStatus: processResult.userStatus
      };

    } catch (error) {
      console.error("Purchase verification error:", error);
      return {
        success: false,
        error: error.message,
        errorCode: "VERIFICATION_FAILED"
      };
    }
  }

  /**
   * Process a validated purchase and update database
   * @param {Object} validationResult - Result from platform validation
   * @param {string} userId - User ID
   * @param {string} deviceId - Device ID
   * @param {string} originalReceipt - Original receipt data
   * @returns {Promise<Object>} Processing result
   */
  async processPurchase(validationResult, userId, deviceId, originalReceipt) {
    try {
      const purchaseInfo = validationResult.purchase;

      // Check if purchase already exists
      let existingPurchase = await PurchaseController.findByTransactionId(purchaseInfo.transactionId);

      if (existingPurchase) {
        // Update existing purchase
        await PurchaseController.validateAndUpdate(
          purchaseInfo.transactionId,
          validationResult.rawResponse
        );
        console.log(`Updated existing purchase: ${purchaseInfo.transactionId}`);
      } else {
        // Create new purchase record
        const purchaseData = {
          user: userId,
          transactionId: purchaseInfo.transactionId,
          originalTransactionId: purchaseInfo.originalTransactionId,
          productId: purchaseInfo.productId,
          platform: validationResult.platform,
          purchaseDate: purchaseInfo.purchaseDate,
          expiryDate: purchaseInfo.expiryDate,
          receiptData: originalReceipt,
          receiptValidated: true,
          validationDate: new Date(),
          status: purchaseInfo.isActive ? "active" : "expired",
          storeData: {
            environment: purchaseInfo.environment,
            bundleId: purchaseInfo.bundleId,
            packageName: purchaseInfo.packageName,
            purchaseToken: purchaseInfo.purchaseToken
          },
          validationResponse: validationResult.rawResponse,
          isAutoRenewing: purchaseInfo.autoRenewing || false,
          willRenew: purchaseInfo.willRenew !== false
        };

        // Handle one-month purchase expiry calculation
        if (purchaseInfo.productId === "one_month_ad_free" && !purchaseInfo.expiryDate) {
          purchaseData.expiryDate = new Date(
            purchaseInfo.purchaseDate.getTime() + this.supportedProducts.one_month_ad_free.duration
          );
        }

        existingPurchase = await PurchaseController.create(purchaseData);
        console.log(`Created new purchase record: ${purchaseInfo.transactionId}`);

        // Add purchase to user's active purchases
        await UserController.addActivePurchase(userId, existingPurchase._id);
      }

      // Update user's IAP status
      const userStatus = await this.updateUserIAPStatus(userId);

      return {
        purchase: existingPurchase,
        userStatus: userStatus
      };

    } catch (error) {
      console.error("Error processing purchase:", error);
      throw error;
    }
  }

  /**
   * Update user's overall IAP status based on active purchases
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Updated user IAP status
   */
  async updateUserIAPStatus(userId) {
    try {
      const iapStatus = await PurchaseController.getUserIAPStatus(userId);

      // Update user record
      await UserController.updateIAPStatus(userId, {
        hasActiveSubscription: iapStatus.hasActiveSubscription,
        subscriptionExpiry: iapStatus.subscriptionExpiry,
        subscriptionProductId: iapStatus.hasActiveSubscription ? "ad_free_subscription" : null,
        hasAdFreeAccess: iapStatus.hasAdFreeAccess,
        oneMonthExpiry: iapStatus.oneMonthExpiry
      });

      console.log(`Updated IAP status for user ${userId}:`, iapStatus);
      return iapStatus;

    } catch (error) {
      console.error("Error updating user IAP status:", error);
      throw error;
    }
  }

  /**
   * Get user's current IAP status
   * @param {string} userId - User ID
   * @returns {Promise<Object>} User's IAP status
   */
  async getUserIAPStatus(userId) {
    try {
      return await PurchaseController.getUserIAPStatus(userId);
    } catch (error) {
      console.error("Error getting user IAP status:", error);
      throw error;
    }
  }

  /**
   * Handle server-to-server notifications from app stores
   * @param {string} platform - Platform (ios/android)
   * @param {Object} notificationData - Notification payload
   * @returns {Promise<Object>} Processing result
   */
  async handleStoreNotification(platform, notificationData) {
    try {
      console.log(`Handling ${platform} store notification`);

      let parsedNotification;

      if (platform === "ios") {
        parsedNotification = appleValidator.parseServerNotification(notificationData);
      } else if (platform === "android") {
        parsedNotification = googleValidator.parseServerNotification(notificationData);
      } else {
        throw new Error(`Unsupported platform: ${platform}`);
      }

      // Process the notification based on type
      await this.processStoreNotification(platform, parsedNotification);

      return { success: true, processed: true };

    } catch (error) {
      console.error("Error handling store notification:", error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Process store notification and update purchase records
   * @param {string} platform - Platform
   * @param {Object} notification - Parsed notification
   */
  async processStoreNotification(platform, notification) {
    try {
      const { transactionId, type, originalTransactionId } = notification;

      if (!transactionId) {
        console.log("No transaction ID in notification, skipping");
        return;
      }

      // For renewals, try to find by original transaction ID first, then by transaction ID
      let purchase;
      if (originalTransactionId) {
        purchase = await PurchaseController.findByOriginalTransactionId(originalTransactionId);
      }

      if (!purchase) {
        purchase = await PurchaseController.findByAnyTransactionId(transactionId);
      }

      if (!purchase) {
        console.log(`Purchase not found for transaction: ${transactionId} or original: ${originalTransactionId}`);
        return;
      }

      console.log(`Processing ${type} notification for purchase: ${purchase.transactionId}`);

      // Handle different notification types
      switch (type) {
        case "SUBSCRIPTION_RENEWED":
        case "DID_RENEW":
          if (notification.expiryDate) {
            await PurchaseController.handleRenewal(purchase.transactionId, notification.expiryDate);
            await this.updateUserIAPStatus(purchase.user);
            console.log(`Subscription renewed for user: ${purchase.user}`);
          }
          break;

        case "SUBSCRIPTION_CANCELED":
        case "CANCEL":
          await PurchaseController.handleCancellation(purchase.transactionId, "User cancelled");
          await this.updateUserIAPStatus(purchase.user);
          console.log(`Subscription cancelled for user: ${purchase.user}`);
          break;

        case "SUBSCRIPTION_EXPIRED":
        case "DID_FAIL_TO_RENEW":
          await PurchaseController.updateStatus(purchase.transactionId, "expired");
          await this.updateUserIAPStatus(purchase.user);
          console.log(`Subscription expired for user: ${purchase.user}`);
          break;

        case "SUBSCRIPTION_REVOKED":
        case "REFUND":
          await PurchaseController.handleRefund(purchase.transactionId, "Store refunded");
          await this.updateUserIAPStatus(purchase.user);
          console.log(`Subscription refunded for user: ${purchase.user}`);
          break;

        default:
          console.log(`Unhandled notification type: ${type} for transaction: ${transactionId}`);
          console.log("Full notification data:", JSON.stringify(notification, null, 2));
      }

    } catch (error) {
      console.error("Error processing store notification:", error);
      console.error("Notification data:", JSON.stringify(notification, null, 2));
      throw error;
    }
  }

  /**
   * Acknowledge Google Play purchase to prevent automatic refunds
   * @param {Object} receiptData - Receipt data containing purchaseToken
   * @param {string} productId - Product ID
   */
  async acknowledgeGooglePlayPurchase(receiptData, productId) {
    try {
      const { purchaseToken } = receiptData;
      const parsedReceipt = JSON.parse(receiptData.receipt || "{}");
      const packageName = parsedReceipt.packageName || process.env.GOOGLE_PLAY_PACKAGE_NAME;

      if (!packageName) {
        throw new Error("Package name not found in receipt or environment");
      }

      await googleValidator.acknowledgePurchase(productId, purchaseToken, packageName);
      console.log(`✅ Google Play purchase acknowledged: ${productId}`);
    } catch (ackError) {
      console.error("❌ CRITICAL: Failed to acknowledge Google Play purchase:", ackError);
      console.error("This will cause automatic refund in 3 days!");
      // Don't throw - we don't want to fail the entire verification
    }
  }

  /**
   * Clean up expired purchases (should be run periodically)
   * @returns {Promise<Object>} Cleanup result
   */
  async cleanupExpiredPurchases() {
    try {
      const result = await PurchaseController.cleanupExpired();
      console.log("Expired purchases cleanup completed");
      return result;
    } catch (error) {
      console.error("Error during cleanup:", error);
      throw error;
    }
  }
}

module.exports = new IAPService();

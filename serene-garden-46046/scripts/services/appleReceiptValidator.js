/* eslint-disable class-methods-use-this */
/* eslint-disable max-statements */
/* eslint-disable max-lines-per-function */
const appleReceiptVerify = require("node-apple-receipt-verify");

class AppleReceiptValidator {
  constructor() {
    // Configure the validator
    appleReceiptVerify.config({
      secret: process.env.APPLE_SHARED_SECRET, // Your app's shared secret from App Store Connect
      environment: ["sandbox", "production"], // Check both environments
      verbose: process.env.NODE_ENV === "development",
      extended: true, // Get extended receipt information
      excludeOldTransactions: false // Include old transactions for validation
    });
  }

  /**
   * Validate an Apple App Store receipt
   * @param {string} receiptData - Base64 encoded receipt data
   * @param {string} productId - Expected product ID
   * @returns {Promise<Object>} Validation result
   */
  async validateReceipt(receiptData, productId) {
    try {
      console.log(`Validating Apple receipt for product: ${productId}`);

      const result = await appleReceiptVerify.validate({
        receipt: receiptData,
        device: false // Set to true if you want to validate device-specific receipts
      });

      console.log("Apple validation result status:", result.status);

      // Check if validation was successful
      if (result.status !== 0) {
        throw new Error(`Apple receipt validation failed with status: ${result.status}`);
      }

      // Extract receipt information
      const receipt = result.receipt;
      const inAppPurchases = receipt.in_app || [];

      // Find the specific purchase for this product
      const purchase = inAppPurchases.find((iap) => iap.product_id === productId);

      if (!purchase) {
        throw new Error(`Product ${productId} not found in receipt`);
      }

      // Parse the purchase information
      const purchaseInfo = {
        transactionId: purchase.transaction_id,
        originalTransactionId: purchase.original_transaction_id,
        productId: purchase.product_id,
        purchaseDate: new Date(parseInt(purchase.purchase_date_ms, 10)),
        expiryDate: purchase.expires_date_ms ? new Date(parseInt(purchase.expires_date_ms, 10)) : null,
        quantity: parseInt(purchase.quantity, 10) || 1,
        isTrialPeriod: purchase.is_trial_period === "true",
        isInIntroOfferPeriod: purchase.is_in_intro_offer_period === "true",
        environment: result.environment,
        bundleId: receipt.bundle_id,
        applicationVersion: receipt.application_version,
        originalApplicationVersion: receipt.original_application_version
      };

      // Check if it's a subscription and if it's still active
      if (purchaseInfo.expiryDate) {
        purchaseInfo.isActive = purchaseInfo.expiryDate > new Date();
        purchaseInfo.isExpired = purchaseInfo.expiryDate <= new Date();
      } else {
        // For non-subscription purchases, check if it's a valid one-time purchase
        purchaseInfo.isActive = true;
        purchaseInfo.isExpired = false;
      }

      return {
        success: true,
        platform: "ios",
        purchase: purchaseInfo,
        rawResponse: result
      };

    } catch (error) {
      console.error("Apple receipt validation error:", error);
      return {
        success: false,
        platform: "ios",
        error: error.message,
        errorCode: error.code || "VALIDATION_FAILED"
      };
    }
  }

  /**
   * Validate a subscription receipt and check renewal status
   * @param {string} receiptData - Base64 encoded receipt data
   * @param {string} productId - Subscription product ID
   * @returns {Promise<Object>} Subscription validation result
   */
  async validateSubscription(receiptData, productId) {
    try {
      const validationResult = await this.validateReceipt(receiptData, productId);

      if (!validationResult.success) {
        return validationResult;
      }

      const purchase = validationResult.purchase;
      // const rawReceipt = validationResult.rawResponse.receipt;

      // Get the latest receipt info for subscription status
      const latestReceiptInfo = validationResult.rawResponse.latest_receipt_info || [];
      const pendingRenewalInfo = validationResult.rawResponse.pending_renewal_info || [];

      // Find the latest transaction for this product
      const latestTransaction = latestReceiptInfo
        .filter((info) => info.product_id === productId)
        .sort((a, b) => parseInt(b.purchase_date_ms, 10) - parseInt(a.purchase_date_ms, 10))[0];

      if (latestTransaction) {
        purchase.transactionId = latestTransaction.transaction_id;
        purchase.purchaseDate = new Date(parseInt(latestTransaction.purchase_date_ms, 10));
        purchase.expiryDate = latestTransaction.expires_date_ms
          ? new Date(parseInt(latestTransaction.expires_date_ms, 10))
          : null;
      }

      // Check renewal status
      const renewalInfo = pendingRenewalInfo.find((info) => info.product_id === productId);
      if (renewalInfo) {
        purchase.willRenew = renewalInfo.auto_renew_status === "1";
        purchase.isInBillingRetryPeriod = renewalInfo.is_in_billing_retry_period === "1";
        purchase.expirationIntent = renewalInfo.expiration_intent;
      }

      return validationResult;

    } catch (error) {
      console.error("Apple subscription validation error:", error);
      return {
        success: false,
        platform: "ios",
        error: error.message,
        errorCode: "SUBSCRIPTION_VALIDATION_FAILED"
      };
    }
  }

  /**
   * Parse Apple Server-to-Server notification
   * @param {Object} notificationPayload - Notification payload from Apple
   * @returns {Object} Parsed notification data
   */
  parseServerNotification(notificationPayload) {
    try {
      const notificationType = notificationPayload.notification_type;
      const receiptData = notificationPayload.unified_receipt;
      const latestReceiptInfo = receiptData?.latest_receipt_info?.[0];

      return {
        type: notificationType,
        environment: notificationPayload.environment,
        receiptData: receiptData,
        bundleId: receiptData?.receipt?.bundle_id,
        productId: latestReceiptInfo?.product_id,
        transactionId: latestReceiptInfo?.transaction_id,
        originalTransactionId: latestReceiptInfo?.original_transaction_id,
        expiryDate: latestReceiptInfo?.expires_date_ms
          ? new Date(parseInt(latestReceiptInfo.expires_date_ms, 10))
          : null
      };
    } catch (error) {
      console.error("Error parsing Apple server notification:", error);
      throw error;
    }
  }
}

module.exports = new AppleReceiptValidator();

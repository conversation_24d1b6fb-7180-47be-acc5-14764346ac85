/* eslint-disable max-statements */
/* eslint-disable class-methods-use-this */
const crypto = require("crypto");

/**
 * Device-based authentication middleware
 * Uses deviceId and channelId for user identification
 */
class AuthMiddleware {
  /**
   * Middleware for device-based authentication
   * Uses deviceId and channelId for basic user identification
   */
  validateDeviceAuth() {
    return (req, res, next) => {
      // Extract deviceId and channelId from params, query, or body (in priority order)
      const deviceId = req.params?.deviceId || req.query?.deviceId || req.body?.deviceId;
      const channelId = req.params?.channelId || req.query?.channelId || req.body?.channelId;

      console.log("validateDeviceAuth");
      console.log("Device ID:", deviceId);
      console.log("Channel ID:", channelId);

      if (!deviceId || !channelId) {
        return res.status(400).json({
          success: false,
          error: "deviceId and channelId are required",
          code: "MISSING_DEVICE_INFO"
        });
      }

      // Basic validation of deviceId format
      if (typeof deviceId !== "string" || deviceId.length < 10) {
        return res.status(400).json({
          success: false,
          error: "Invalid deviceId format",
          code: "INVALID_DEVICE_ID"
        });
      }

      // Basic validation of channelId
      if (typeof channelId !== "string" || channelId.length === 0) {
        return res.status(400).json({
          success: false,
          error: "Invalid channelId format",
          code: "INVALID_CHANNEL_ID"
        });
      }

      req.deviceId = deviceId;
      req.channelId = channelId;
      next();
    };
  }
}

// Create singleton instance
const authMiddleware = new AuthMiddleware();

/**
 * Request validation middleware
 */
function validateRequest(req, res, next) {
  // Basic request validation
  if (req.method === "POST" && !req.body) {
    return res.status(400).json({
      success: false,
      error: "Request body is required",
      code: "MISSING_BODY"
    });
  }

  // Add request ID for tracking
  req.requestId = crypto.randomBytes(8).toString("hex");

  // Add timestamp
  req.timestamp = new Date().toISOString();

  next();
}

/**
 * Security headers middleware
 */
function securityHeaders(req, res, next) {
  // Add security headers
  res.set({
    "X-Content-Type-Options": "nosniff",
    "X-Frame-Options": "DENY",
    "X-XSS-Protection": "1; mode=block",
    "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
    "Content-Security-Policy": "default-src 'self'",
    "X-Request-ID": req.requestId
  });

  next();
}

module.exports = {
  authMiddleware,
  validateRequest,
  securityHeaders
};

const { v4: uuidv4 } = require("uuid");

/**
 * Global error handling middleware for IAP operations
 * Prevents server crashes and provides graceful error responses
 */
class IAPErrorHandler {
    /**
     * Handle synchronous errors
     */
    static handleError(error, req, res) {
        const errorId = uuidv4();
        const timestamp = new Date().toISOString();

        // Log the error with context
        console.error(`[${timestamp}] [ErrorID: ${errorId}] IAP Error:`, {
            message: error.message,
            stack: error.stack,
            url: req?.originalUrl,
            method: req?.method,
            ip: req?.ip,
            userAgent: req?.get("User-Agent")
        });

        // Don't expose internal errors in production
        const isProduction = process.env.NODE_ENV === "production";
        const errorMessage = isProduction ? "Internal server error" : error.message;

        // Determine appropriate status code
        let statusCode = 500;
        if (error.name === "ValidationError") statusCode = 400;
        if (error.name === "NotFoundError") statusCode = 404;
        if (error.name === "UnauthorizedError") statusCode = 401;

        res.status(statusCode).json({
            success: false,
            error: errorMessage,
            errorId: errorId,
            timestamp: timestamp,
            ...(!isProduction && { details: error.stack })
        });
    }

    /**
     * Handle unhandled promise rejections
     */
    static handleUnhandledRejection(reason, promise) {
        const errorId = uuidv4();
        const timestamp = new Date().toISOString();

        console.error(`[${timestamp}] [ErrorID: ${errorId}] Unhandled Promise Rejection:`, {
            reason: reason,
            promise: promise,
            stack: reason?.stack
        });

        // In production, you might want to restart the process or notify monitoring
        if (process.env.NODE_ENV === "production") {
            process.exit(1); // Restart the process
        }
    }

    /**
     * Handle uncaught exceptions
     */
    static handleUncaughtException(error) {
        const errorId = uuidv4();
        const timestamp = new Date().toISOString();

        console.error(`[${timestamp}] [ErrorID: ${errorId}] Uncaught Exception:`, {
            message: error.message,
            stack: error.stack
        });

        // In production, restart the process
        if (process.env.NODE_ENV === "production") {
            process.exit(1);
        }
    }

    /**
     * Async error wrapper for Express routes
     */
    static asyncHandler(fn) {
        return (req, res, next) => {
            Promise.resolve(fn(req, res, next)).catch(next);
        };
    }

    /**
     * Create a safe function that never throws
     */
    static safeAsync(fn, fallbackValue = null) {
        return async (...args) => {
            try {
                return await fn(...args);
            } catch (error) {
                const errorId = uuidv4();
                console.error(`[SafeAsync ErrorID: ${errorId}] Function failed:`, error);
                return fallbackValue;
            }
        };
    }

    /**
     * Initialize global error handlers
     */
    static initialize() {
        process.on("unhandledRejection", this.handleUnhandledRejection.bind(this));
        process.on("uncaughtException", this.handleUncaughtException.bind(this));

        console.log("Global error handlers initialized");
    }
}

module.exports = IAPErrorHandler;

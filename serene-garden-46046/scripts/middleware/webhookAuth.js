const crypto = require("crypto");
const { promisify } = require("util");
const verify = promisify(crypto.verify);

/**
 * Middleware to verify Apple App Store Server-to-Server notification signatures
 */
function verifyAppleWebhook(req, res, next) {
  try {
    // Apple doesn't use signature verification in the same way as other services
    // Instead, they recommend validating the receipt data itself
    // For now, we'll just log and proceed, but you should implement proper validation
    console.log("Apple webhook received, proceeding with validation");
    next();
  } catch (error) {
    console.error("Apple webhook verification failed:", error);
    return res.status(401).json({ error: "Webhook verification failed" });
  }
}

/**
 * Google Play IAP public key for signature verification
 * This should be stored in environment variables in production
 */
const GOOGLE_PLAY_PUBLIC_KEY = process.env.GOOGLE_PLAY_PUBLIC_KEY || `
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtXoxNuW/j2LmpU+DAaXc
7NEi/3GsyTOGLO6oaW71TiHQMG+cGvy6lkCxv0DZXl75Q4A1wEhMoVqFdx99VIMb
rV0wvhJsYiIiab25z8Wmj4JqIozv6KAZvXqOQNOCFRZzbRqBS6Umc7k7xYFa7hLj
KcY4RZQgUZNcWxs6/r6G8mM7gc+O95Sb2RY6U/luiIvqDOoJPOiF5Rn66O0vmeFe
3QKVHbFf6RsrsBmh42nrSguUL3gHUc19BhflTztTbsSOdjdVfcArZqKXUTkyOSvo
9vRlLguVS3ovXgDti51ezcob1vtDbyBdCjziLVCv7b51WT+XUjmtWo6sK4EhdJQc
9wIDAQAB
`.trim();

/**
 * Expected Google Pub/Sub topic for IAP notifications
 */
const EXPECTED_TOPIC = process.env.GOOGLE_IAP_TOPIC || "projects/friendlychatclient-311211/topics/iap_notifications";

/**
 * Middleware to verify Google Play Developer Notification signatures
 */
async function verifyGoogleWebhook(req, res, next) {
  try {
    const message = req.body.message;

    validateMessageAndTopic(message, res);

    await validateSignatureIfPresent(message);

    decodeAndProcessMessage(message, req);

    console.log("Google Play webhook verified successfully");
    next();
  } catch (error) {
    console.error("Google webhook verification failed:", error);
    return res.status(401).json({ error: "Webhook verification failed" });
  }
}

/**
 * Validate message format and topic
 */
function validateMessageAndTopic(message, res) {
  if (!validateMessageFormat(message)) {
    res.status(400).json({ error: "Invalid Google Play notification format" });
    throw new Error("Invalid format");
  }

  if (!validateTopic(message)) {
    res.status(400).json({ error: "Invalid notification topic" });
    throw new Error("Invalid topic");
  }
}

/**
 * Validate the basic message format
 */
function validateMessageFormat(message) {
  return message && message.data;
}

/**
 * Validate the topic matches expected topic
 */
function validateTopic(message) {
  if (message.attributes && message.attributes.topic !== EXPECTED_TOPIC) {
    console.warn(`Unexpected topic: ${message.attributes.topic}, expected: ${EXPECTED_TOPIC}`);
    return false;
  }
  return true;
}

/**
 * Validate signature if present
 */
async function validateSignatureIfPresent(message) {
  if (message.attributes && message.attributes.signature) {
    const isValid = await verifyGoogleSignature(message);
    if (!isValid) {
      console.error("Google Play webhook signature validation failed");
      throw new Error("Invalid signature");
    }
    console.log("Google Play webhook signature validated successfully");
  } else {
    console.warn("Google Play webhook received without signature - proceeding without validation");
  }
}

/**
 * Decode and process the Google Play notification message
 */
function decodeAndProcessMessage(message, req) {
  try {
    const decodedData = Buffer.from(message.data, "base64").toString();
    console.log("Google Play webhook received successfully", decodedData);

    const notificationData = JSON.parse(decodedData);

    // Attach the decoded data to the request for use in the controller
    req.decodedNotification = notificationData;
  } catch (decodeError) {
    console.error("Failed to decode Google Play notification:", decodeError);
    throw new Error("Invalid notification data");
  }
}

/**
 * Verify Google Play webhook signature using RSA public key
 */
async function verifyGoogleSignature(message) {
  const signature = message.attributes?.signature;
  const signatureData = message.attributes?.signatureData;

  if (!signature || !signatureData) {
    console.warn("Missing signature or signatureData for verification");
    return false;
  }

  try {
    return await performSignatureVerification(signature, message.data);
  } catch (error) {
    console.error("Signature verification error:", error);
    return false;
  }
}

/**
 * Perform the actual RSA signature verification
 */
async function performSignatureVerification(signature, dataToVerify) {
  const signatureBuffer = Buffer.from(signature, "base64");
  const publicKey = `-----BEGIN PUBLIC KEY-----\n${GOOGLE_PLAY_PUBLIC_KEY}\n-----END PUBLIC KEY-----`;

  const isValid = await verify(
    "RSA-SHA256",
    Buffer.from(dataToVerify),
    publicKey,
    signatureBuffer
  );

  return isValid;
}

/**
 * General webhook rate limiting middleware
 */
function webhookRateLimit(req, res, next) {
  const clientIP = req.ip || req.connection.remoteAddress;
  const now = Date.now();
  const windowMs = 60 * 1000;
  const maxRequests = 100;

  initializeRateLimitIfNeeded();

  const clientData = getOrCreateClientData(clientIP, now, windowMs);

  updateClientRequestCount(clientIP, clientData, { now, windowMs });

  if (isRateLimitExceeded(clientData, maxRequests)) {
    return res.status(429).json({ error: "Rate limit exceeded" });
  }

  next();
}

/**
 * Initialize rate limit storage if needed
 */
function initializeRateLimitIfNeeded() {
  if (!global.webhookRateLimit) {
    global.webhookRateLimit = new Map();
  }
}

/**
 * Get or create client rate limit data
 */
function getOrCreateClientData(clientIP, now, windowMs) {
  return global.webhookRateLimit.get(clientIP) || { count: 0, resetTime: now + windowMs };
}

/**
 * Update client request count and reset time if needed
 */
function updateClientRequestCount(clientIP, clientData, timeConfig) {
  if (timeConfig.now > clientData.resetTime) {
    clientData.count = 1;
    clientData.resetTime = timeConfig.now + timeConfig.windowMs;
  } else {
    clientData.count += 1;
  }
  global.webhookRateLimit.set(clientIP, clientData);
}

/**
 * Check if rate limit is exceeded
 */
function isRateLimitExceeded(clientData, maxRequests) {
  return clientData.count > maxRequests;
}

/**
 * Middleware to validate webhook content type
 */
function validateWebhookContentType(req, res, next) {
  const contentType = req.get("Content-Type");

  if (!contentType || !contentType.includes("application/json")) {
    return res.status(400).json({ error: "Content-Type must be application/json" });
  }

  next();
}

/**
 * Middleware to log webhook requests for debugging
 */
function logWebhookRequest(req, res, next) {
  const timestamp = new Date().toISOString();
  const method = req.method;
  const url = req.url;
  const userAgent = req.get("User-Agent") || "Unknown";
  const contentLength = req.get("Content-Length") || "0";

  console.log(`[${timestamp}] Webhook ${method} ${url} - User-Agent: ${userAgent}, Content-Length: ${contentLength}`);

  if (process.env.NODE_ENV === "development") {
    console.log("Webhook Headers:", JSON.stringify(req.headers, null, 2));
  }

  next();
}

/**
 * Error handling middleware for webhooks
 */
function webhookErrorHandler(error, req, res) {
  console.error("Webhook Error:", error);

  res.status(500).json({
    error: "Internal server error",
    timestamp: new Date().toISOString()
  });
}

module.exports = {
  verifyAppleWebhook,
  verifyGoogleWebhook,
  webhookRateLimit,
  validateWebhookContentType,
  logWebhookRequest,
  webhookErrorHandler
};

/* eslint-disable class-methods-use-this */
/* eslint-disable max-lines-per-function */
/* eslint-disable max-statements */
/**
 * Simple in-memory rate limiter middleware
 * In production, consider using Redis-based rate limiting
 */

class RateLimiter {
  constructor() {
    this.clients = new Map();
    this.cleanupInterval = setInterval(() => this.cleanup(), 60000); // Cleanup every minute
  }

  /**
   * Create rate limiting middleware
   * @param {Object} options - Rate limiting options
   * @param {number} options.windowMs - Time window in milliseconds
   * @param {number} options.maxRequests - Maximum requests per window
   * @param {string} options.message - Error message when limit exceeded
   * @returns {Function} Express middleware
   */
  createLimiter(options = {}) {
    const {
      windowMs = 15 * 60 * 1000, // 15 minutes
      maxRequests = 100,
      message = "Too many requests, please try again later"
    } = options;

    return (req, res, next) => {
      const clientIP = this.getClientIP(req);
      const now = Date.now();
      const windowStart = now - windowMs;

      // Get or create client record
      let clientData = this.clients.get(clientIP);
      if (!clientData) {
        clientData = { requests: [], blocked: false, blockExpiry: 0 };
        this.clients.set(clientIP, clientData);
      }

      // Check if client is currently blocked
      if (clientData.blocked && now < clientData.blockExpiry) {
        return res.status(429).json({
          success: false,
          error: message,
          retryAfter: Math.ceil((clientData.blockExpiry - now) / 1000)
        });
      }

      // Remove old requests outside the window
      clientData.requests = clientData.requests.filter((timestamp) => timestamp > windowStart);

      // Check if limit exceeded
      if (clientData.requests.length >= maxRequests) {
        // Block client for the window duration
        clientData.blocked = true;
        clientData.blockExpiry = now + windowMs;

        console.log(`Rate limit exceeded for IP: ${clientIP}`);

        return res.status(429).json({
          success: false,
          error: message,
          retryAfter: Math.ceil(windowMs / 1000)
        });
      }

      // Add current request
      clientData.requests.push(now);
      clientData.blocked = false;

      // Add rate limit headers
      res.set({
        "X-RateLimit-Limit": maxRequests,
        "X-RateLimit-Remaining": Math.max(0, maxRequests - clientData.requests.length),
        "X-RateLimit-Reset": new Date(now + windowMs).toISOString()
      });

      next();
    };
  }

  /**
   * Get client IP address
   * @param {Object} req - Express request object
   * @returns {string} Client IP address
   */
  getClientIP(req) {
    return req.ip ||
      req.connection.remoteAddress ||
      req.socket.remoteAddress ||
      (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
      "0.0.0.0";
  }

  /**
   * Clean up old client records
   */
  cleanup() {
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours

    for (const [clientIP, clientData] of this.clients.entries()) {
      // Remove clients that haven't made requests in the last 24 hours
      const lastRequest = Math.max(...clientData.requests, 0);
      if (now - lastRequest > maxAge) {
        this.clients.delete(clientIP);
      }
    }

    console.log(`Rate limiter cleanup: ${this.clients.size} active clients`);
  }

  /**
   * Get current statistics
   * @returns {Object} Rate limiter statistics
   */
  getStats() {
    const now = Date.now();
    let totalRequests = 0;
    let blockedClients = 0;

    for (const clientData of this.clients.values()) {
      totalRequests += clientData.requests.length;
      if (clientData.blocked && now < clientData.blockExpiry) {
        blockedClients++;
      }
    }

    return {
      activeClients: this.clients.size,
      totalRequests,
      blockedClients,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Clear all rate limit data (for testing)
   */
  clear() {
    this.clients.clear();
  }

  /**
   * Destroy the rate limiter and cleanup interval
   */
  destroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    this.clients.clear();
  }
}

// Create singleton instance
const rateLimiter = new RateLimiter();

// Predefined limiters for different use cases
const limiters = {
  // General API rate limiting
  general: rateLimiter.createLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100,
    message: "Too many requests, please try again later"
  }),

  // Strict rate limiting for purchase verification
  purchase: rateLimiter.createLimiter({
    windowMs: 5 * 60 * 1000, // 5 minutes
    maxRequests: 10,
    message: "Too many purchase verification attempts, please try again later"
  }),

  // Webhook rate limiting
  webhook: rateLimiter.createLimiter({
    windowMs: 1 * 60 * 1000, // 1 minute
    maxRequests: 50,
    message: "Webhook rate limit exceeded"
  }),

  // Status check rate limiting
  status: rateLimiter.createLimiter({
    windowMs: 1 * 60 * 1000, // 1 minute
    maxRequests: 20,
    message: "Too many status check requests"
  })
};

module.exports = {
  rateLimiter,
  limiters
};

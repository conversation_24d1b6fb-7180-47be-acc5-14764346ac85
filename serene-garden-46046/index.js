const mongoose = require("mongoose"),
  port = process.env.PORT || 3000,
  { server } = require("./app.socket.io");
require("dotenv").config();

// Initialize IAP cleanup service
const cleanupService = require("./scripts/services/cleanupService");
// mongoose.set("useNewUrlParser", true);
// mongoose.set("useFindAndModify", false);
// mongoose.set("useCreateIndex", true);
// mongoose.set("useUnifiedTopology", true);
mongoose.connect(
  process.env.mongoDBURI,
  { dbName: "chatNBot" },
  handleConnection
);
function handleConnection(err) {
  if (err) throw err;
  console.log("Connected to MongoDB successfully");

  // Start IAP cleanup service after database connection
  cleanupService.start();
}
module.exports = server.listen(port, () =>
  console.log(`Example app listening on port ${port}!`)
);

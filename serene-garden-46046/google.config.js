/* eslint-disable no-undef */
/* eslint-disable require-unicode-regexp */
/* eslint-disable max-statements */
/* eslint-disable func-style */
const { Storage } = require("@google-cloud/storage");
const storage = new Storage({
  projectId: "friendlychatclient-311211",
  keyFilename: "./credentials.json",
});
const bucket = storage.bucket("friendly-chat-images");

const uploadImg = async (img, deviceid, name) => {
  // process.env['NODE_TLS_REJECT_UNAUTHORIZED'] = 0
  // Create a new blob in the bucket and upload the file data.
  const file = bucket.file(`imgs/${deviceid}-${name}`);
  const buf = Buffer.from(img, "base64"); // Added
  await file.save(buf);
  return `https://storage.googleapis.com/${bucket.name}/${file.name}`;
};

module.exports.uploadImg = uploadImg;

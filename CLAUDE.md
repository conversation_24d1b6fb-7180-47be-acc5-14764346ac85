# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a two-part application:

1. **friendlyChatClient** - React Native mobile app for anonymous chatting
2. **serene-garden-46046** - Node.js backend server with Socket.IO for real-time messaging

## Architecture

### Frontend (React Native)

- **Framework**: React Native 0.76.6 with React 18.1.0
- **State Management**: Recoil for global state
- **Navigation**: React Navigation (Native Stack, Material Top Tabs)
- **Real-time**: Socket.IO client for WebSocket connections
- **Styling**: React Native Paper component library
- **Build Variants**: Full and Lite versions with different app IDs

### Backend (Node.js/Express)

- **Framework**: Express.js with Socket.IO
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: Custom middleware with rate limiting
- **IAP Services**: Apple and Google Play purchase validation
- **File Storage**: Google Cloud Storage

## Development Commands

### Frontend (friendlyChatClient)

```bash
# Start development server
npm start

# Run Android (Full version)
yarn android:full

# Run Android (Lite version)
yarn android:lite

# Run iOS
yarn ios

# Run tests
npm test

# Lint code
yarn lint

# Build release versions
yarn release:full
yarn release:lite
```

### Backend (serene-garden-46046)

```bash
# Start server
yarn nodemon

# Start with nodemon (auto-restart)
yarn nodemon

# Run tests
npm test

# Start ngrok tunnel
npm run ngrok
```

## Key Features

### Frontend Components

- Real-time chat with typing indicators
- Media sharing (images from camera/gallery)
- Emoji selector and profanity filtering
- In-app purchases for premium features
- Push notifications
- Multiple user profiles with avatar generation

### Backend Services

- WebSocket-based real-time messaging
- User matching and chat room management
- Purchase validation (Apple/Google)
- Content moderation with profanity filtering
- Database models for users, chats, purchases
- Health checks and circuit breakers

## File Structure Highlights

### Frontend

- `src/Components/` - UI components and chat interfaces
- `src/services/` - Socket.IO and backend API services
- `src/store/` - Recoil state management
- `src/pages/` - Main application screens

### Backend

- `scripts/DB/` - Database models and controllers
- `scripts/middleware/` - Authentication and error handling
- `scripts/services/` - IAP validation and external services
- `scripts/utils/` - Utility functions and health checks

## Testing

- **Frontend**: Jest with React Native testing utilities
- **Backend**: Jest with Supertest for API testing
- Test files located in `__tests__/` directories

## Build Configuration

- Android: Multiple build variants (full/lite) with different package names
- iOS: Standard React Native Xcode project setup
- Environment variables managed via react-native-config

## Dependencies

- **Frontend**: Extensive React Native ecosystem with camera, ads, IAP, and UI libraries
- **Backend**: Express, Socket.IO, MongoDB, and cloud service integrations

## Important Notes

- Uses patch-package for dependency patching (check patches/ directory)
- Multiple app variants require different configuration files
- IAP services require proper credential setup for Apple/Google
- Real-time features depend on WebSocket connections to backend
[byterover-mcp]

# important 
always use byterover-retrieve-knowledge tool to get the related context before any tasks 
always use byterover-store-knowledge to store all the critical informations after sucessful tasks
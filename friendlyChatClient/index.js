/**
 * @format
 */

import { AppRegistry } from 'react-native';
import Main from './src/Main';

import Config from "react-native-config";
import notifee, { AndroidImportance } from '@notifee/react-native';
import { MobileAds } from 'react-native-google-mobile-ads';
import remoteConfig from '@react-native-firebase/remote-config';
//flatlist freeze bug

// eslint-disable-next-line no-restricted-imports
import ViewReactNativeStyleAttributes from 'react-native/Libraries/Components/View/ReactNativeStyleAttributes';
ViewReactNativeStyleAttributes.scaleY = true;
console.log("env", JSON.stringify(Config));
globalThis.RNFB_SILENCE_MODULAR_DEPRECATION_WARNINGS = true;
const {
  appName,
  AdInterval,
  AppOpenAdId,
  AdInterstitial,
  AdBanner,
  BoredBannerOne,
  BoredBannerTwo,
  BoredNative,
  BoredNativeTwo,
  BoredNativeOne,
} = Config;
remoteConfig()
  .setDefaults({
    AdInterval,
    AppOpenAdId,
    AdInterstitial,
    AdBanner,
    BoredBannerOne,
    BoredBannerTwo,
    BoredNative,
    BoredNativeOne,
    BoredNativeTwo,
    addGamesTab: false,
  })
  .then(() => remoteConfig().fetchAndActivate());
MobileAds().initialize();

// Initialize Notifee
async function initializeNotifications() {
  // Request permissions
  await notifee.requestPermission();

  // Create notification channel for Android
  await notifee.createChannel({
    id: 'default-channel-id',
    name: 'Default channel',
    sound: 'default',
    importance: AndroidImportance.HIGH,
    vibration: true,
  });

  console.log('Notifee initialized successfully');
}

// Initialize notifications
initializeNotifications().catch(console.error);
console.log(`App Name: ${appName}`);

AppRegistry.registerComponent(appName, () => Main);

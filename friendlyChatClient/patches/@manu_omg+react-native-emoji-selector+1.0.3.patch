diff --git a/node_modules/@manu_omg/react-native-emoji-selector/index.js b/node_modules/@manu_omg/react-native-emoji-selector/index.js
index fc254e9..3eda8bb 100644
--- a/node_modules/@manu_omg/react-native-emoji-selector/index.js
+++ b/node_modules/@manu_omg/react-native-emoji-selector/index.js
@@ -95,7 +95,7 @@ const TabBar = ({ theme, activeCategory, showHistory, onPress, width, categoryBu
               textAlign: "center",
               paddingBottom: 8,
               paddingTop: 3,
-              fontSize: tabSize - 20,
+              fontSize: Math.max(tabSize - 24, 18),
               ...categoryTextStyle
             }}
           >
@@ -113,11 +113,12 @@ const EmojiCell = ({ emoji, colSize, ...other }) => (
       width: colSize,
       height: colSize,
       alignItems: "center",
-      justifyContent: "center"
+      justifyContent: "center",
+      paddingHorizontal:6
     }}
     {...other}
   >
-    <Text style={{ color: "#FFFFFF", fontSize: colSize - 12 }}>
+    <Text style={{ color: "#FFFFFF", fontSize: Math.max(colSize - 20, 6) }}>
       {charFromEmojiObject(emoji)}
     </Text>
   </TouchableOpacity>
@@ -134,7 +135,9 @@ export default class EmojiSelector extends Component {
     colSize: 0,
     width: 0
   };
-
+  shouldComponentUpdate(nextProps,nextState) {
+    return !(this.state.isReady&&nextState.category===this.state.category);
+  }
   //
   //  HANDLER METHODS
   //

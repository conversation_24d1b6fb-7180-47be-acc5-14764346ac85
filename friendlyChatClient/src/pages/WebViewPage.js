import React, { useCallback, useRef } from 'react';
import { BackHandler, StyleSheet, View } from 'react-native';
import { WebView } from 'react-native-webview';
import { useFocusEffect } from '@react-navigation/native';
import { useRecoilValue } from 'recoil';
import { LinksState } from '../store/tooltip/atoms';
import { ActivityIndicator } from 'react-native-paper';
import analytics from '@react-native-firebase/analytics';

export default function WebViewPage(params) {
  const links = useRecoilValue(LinksState);
  const webViewRef = useRef();
  useFocusEffect(
    useCallback(() => {
      const handleBackButtonPress = () => {
        try {
          webViewRef.current?.goBack();
        } catch (err) {
          console.log('[handleBackButtonPress] Error : ', err.message);
        }
        return true;
      };

      analytics().logEvent('other_tab');
      BackHandler.addEventListener('hardwareBackPress', handleBackButtonPress);

      return () =>
        BackHandler.removeEventListener(
          'hardwareBackPress',
          handleBackButtonPress,
        );
    }, []),
  );
  return (
    <>
      <WebView
        style={styles.webview}
        androidLayerType="hardware"
        ref={webViewRef}
        originWhiteList={['*']}
        source={{
          uri: links[params.route.name],
        }}
        startInLoadingState={true}
        renderLoading={() => (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size={50} color="#a9a9a9" />
          </View>
        )}
        userAgent="Mozilla/5.0 (Linux; Android;) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/67.0.3396.99 Mobile Safari/537.36 Friendchat/Android "
      />
    </>
  );
}

const styles = StyleSheet.create({
  webview: {
    opacity: 0.99,
  },
  loadingContainer: {
    position: 'absolute',
    height: '100%',
    width: '100%',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

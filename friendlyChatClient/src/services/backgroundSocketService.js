import notifee, { EventType } from '@notifee/react-native';
import { AppState, Platform } from 'react-native';
import SocketService, { isSocketConnected } from './socketIoService';

class BackgroundSocketService {
    constructor() {
        this.isForegroundServiceRunning = false;
        this.appState = AppState.currentState;
        this.foregroundServiceTimeout = null;
        this.setupAppStateListener();
        this.registerForegroundService();
    }

    registerForegroundService() {
        // Register foreground service handler
        notifee.registerForegroundService(() => {
            return new Promise(() => {
                // This keeps the foreground service running
                // The service will be stopped when stopForegroundService() is called
                console.log('Foreground service registered for socket maintenance');
            });
        });
    }

    setupAppStateListener() {
        AppState.addEventListener('change', async (nextAppState) => {
            console.log('App state changed:', this.appState, '->', nextAppState);

            try {
                if (this.appState.match(/inactive|background/) && nextAppState === 'active') {
                    // App came to foreground - stop foreground service
                    console.log('Foreground service stopped for socket maintenance');
                    await this.stopForegroundService();
                } else if (this.appState === 'active' && nextAppState.match(/inactive|background/)) {
                    // App going to background - start foreground service to maintain connection
                    console.log('Foreground service started for socket maintenance');
                    await this.startForegroundService();
                }
            } catch (error) {
                console.error('Error handling app state change:', error);
            } finally {
                this.appState = nextAppState;
            }
        });
    }

    async startForegroundService() {
        if (this.isForegroundServiceRunning) return;

        try {
            // Create a channel (required for Android)
            const channelId = await notifee.createChannel({
                id: 'socket-service',
                name: 'Socket Connection Service',
                importance: 2, // IMPORTANCE_LOW for Android
            });

            // Start foreground service - ensure this completes within Android's time limit
            await notifee.displayNotification({
                id: 'socket-service',
                title: 'Maintaining chat connection',
                body: 'Your chat session is being maintained in background',
                android: {
                    channelId,
                    asForegroundService: true,
                    ongoing: true,
                    autoCancel: false,
                    importance: 2, // IMPORTANCE_LOW
                    smallIcon: 'ic_notification',
                    largeIcon: 'ic_notification',
                    pressAction: {
                        id: 'default',
                    },
                },
            });

            this.isForegroundServiceRunning = true;
            console.log('Foreground service started for socket maintenance');

            // Set a timeout to automatically stop the service before Android's 3-minute limit
            // Stop after 2.5 minutes (150 seconds) to prevent timeout crash
            this.foregroundServiceTimeout = setTimeout(async () => {
                console.warn('Auto-stopping foreground service to prevent timeout');
                await this.stopForegroundService();
            }, 150000); // 2.5 minutes

        } catch (error) {
            console.error('Failed to start foreground service:', error);
            // Reset the flag if service failed to start
            this.isForegroundServiceRunning = false;

            // Re-throw the error to ensure proper error handling
            throw error;
        }
    }

    async stopForegroundService() {
        if (!this.isForegroundServiceRunning) return;

        // Clear the timeout if it exists
        if (this.foregroundServiceTimeout) {
            clearTimeout(this.foregroundServiceTimeout);
            this.foregroundServiceTimeout = null;
        }

        try {
            // Use the proper method to stop foreground service
            await notifee.stopForegroundService();
            this.isForegroundServiceRunning = false;
            console.log('Foreground service stopped successfully');
        } catch (error) {
            console.error('Failed to stop foreground service:', error);

            // Check if this is a foreground service timeout error
            if (error.message && error.message.includes('FOREGROUND_SERVICE_TYPE_SHORT_SERVICE') &&
                error.message.includes('did not stop within a timeout')) {
                console.warn('Foreground service timeout detected - dismissing notification');
                await this.dismissNotification();
            }

            // Reset flag even if there's an error to avoid stuck state
            this.isForegroundServiceRunning = false;
        }
    }

    async dismissNotification() {
        try {
            // Cancel the notification to prevent the app from crashing
            await notifee.cancelNotification('socket-service');
            console.log('Notification dismissed due to foreground service timeout');
        } catch (dismissError) {
            console.error('Failed to dismiss notification:', dismissError);
        }
    }

}

// Export singleton instance
export default new BackgroundSocketService();
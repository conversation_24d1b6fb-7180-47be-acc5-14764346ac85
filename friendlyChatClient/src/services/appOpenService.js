import { AppState } from 'react-native';
import { AppOpenAd, AdEventType } from 'react-native-google-mobile-ads';
import remoteConfig from '@react-native-firebase/remote-config';
import { getAdFreeStatus } from '../contexts/IAPContext';

let retryAttempt = 0;
let appOpenAd;
let appOpenInitialized = false;
let appStateSubscription = null;
let currentAppState = AppState.currentState;

// Initialize app open ad (call this once at app startup)
export const initializeAppOpenAd = () => {
    if (appOpenInitialized) return;

    remoteConfig()
        .ensureInitialized()
        .finally(() => {
            appOpenAd = AppOpenAd.createForAdRequest(
                remoteConfig().getValue('AppOpenAdId').asString(),
            );
            appOpenInitialized = true;
        });
};

// Handle app open logic
export const handleAppOpen = (showAds) => {
    if (!appOpenAd || !appOpenInitialized) {
        console.log('App open ad not initialized yet');
        return;
    }

    const isAdFree = getAdFreeStatus();
    console.log('App open: isAdFree', isAdFree);

    const checkAdsPreference = async () => {
        try {
            if (!isAdFree) {
                console.log('No valid ad-free status found, showing ads');
                initializeAd();
            } else {
                console.log('User has ad-free status, skipping ads');
            }
        } catch (error) {
            console.error('Error checking ads preference:', error);
            // On error, default to showing ads
            initializeAd();
        }
    };

    const initializeAd = () => {
        if (appOpenAd && showAds) {
            console.log('Initializing app open ad');
            try {
                if (!appOpenAd.loaded) {
                    appOpenAd.addAdEventListener(AdEventType.ERROR, error => {
                        try {
                            retryAttempt += 1;
                            const retryDelay = Math.pow(2, Math.min(6, retryAttempt));
                            setTimeout(function () {
                                appOpenAd.load();
                            }, retryDelay * 1000);
                        } catch (err) {
                            console.log('Error handling ad error:', err);
                        }
                    });

                    appOpenAd.addAdEventListener(AdEventType.CLOSED, () => {
                        try {
                            appOpenAd.load();
                        } catch (error) {
                            console.log('Error reloading ad after close:', error);
                        }
                    });
                    appOpenAd.load();
                }
            } catch (error) {
                console.error('Error initializing ad:', error);
            }
        }
    };

    checkAdsPreference();
};

// Show ad if ready
export const showAdIfReady = () => {
    try {
        if (appOpenAd && appOpenAd.loaded) {
            appOpenAd.show();
        } else if (appOpenAd) {
            appOpenAd.load();
        }
    } catch (error) {
        console.log('Error showing ad:', error);
    }
};

// Setup app state change listener for showing ads when app comes to foreground
export const setupAppStateListener = () => {
    if (appStateSubscription) {
        return; // Already set up
    }

    appStateSubscription = AppState.addEventListener('change', nextAppState => {
        if (
            currentAppState.match(/inactive|background/) &&
            nextAppState === 'active'
        ) {
            // App came to foreground, check if we should show an ad
            const isAdFree = getAdFreeStatus();
            if (!isAdFree && appOpenAd && appOpenAd.loaded) {
                showAdIfReady();
            }
        }
        currentAppState = nextAppState;
    });
};

// Cleanup app state listener
export const cleanupAppStateListener = () => {
    if (appStateSubscription) {
        appStateSubscription.remove();
        appStateSubscription = null;
    }
};
function capFirst(string) {
  if (string) {
    return string.charAt(0).toUpperCase() + string.slice(1);
  }
}

function getRandomInt(min, max) {
  return Math.floor(Math.random() * (max - min)) + min;
}

export function generateName() {
  var name1 = [
    'Excited',
    'Anxious',
    'Overweight',
    'Demonic',
    'Jumpy',
    'Misunderstood',
    'Squashed',
    'Gargantuan',
    'Broad',
    'Crooked',
    'Curved',
    'Deep',
    'Even',
    'Excited',
    'Anxious',
    'Overweight',
    'Demonic',
    'Jumpy',
    'Misunderstood',
    'Squashed',
    'Gargantuan',
    'Broad',
    'Crooked',
    'Curved',
    'Deep',
    'Even',
    'Flat',
    '<PERSON><PERSON>',
    'Jagged',
    'Round',
    'Shallow',
    'Square',
    'Steep',
    'Straight',
    'Thick',
    'Thin',
    'Cooing',
    'Deafening',
    'Faint',
    'Harsh',
    'High-pitched',
    '<PERSON><PERSON>',
    'Hu<PERSON>',
    '<PERSON><PERSON>',
    'Loud',
    '<PERSON>od<PERSON>',
    '<PERSON>aning',
    'Mu<PERSON>',
    'Noisy',
    'Purring',
    'Quiet',
    '<PERSON><PERSON><PERSON>',
    '<PERSON>ree<PERSON>',
    'Shrill',
    '<PERSON>',
    '<PERSON>',
    'Squeaky',
    'Squealing',
    'Thundering',
    'Voiceless',
    'Whispering',
  ];

  var name2 = [
    'Taco',
    'Operating System',
    'Sphere',
    'Watermelon',
    'Cheeseburger',
    'Apple Pie',
    'Spider',
    'Dragon',
    'Remote Control',
    'Soda',
    'Barbie Doll',
    'Watch',
    'Purple Pen',
    'Dollar Bill',
    'Stuffed Animal',
    'Hair Clip',
    'Sunglasses',
    'T-shirt',
    'Purse',
    'Towel',
    'Hat',
    'Camera',
    'Hand Sanitizer Bottle',
    'Photo',
    'Dog Bone',
    'Hair Brush',
    'Birthday Card',
  ];

  var name =
    capFirst(name1[getRandomInt(0, name1.length + 1)]) +
    ' ' +
    capFirst(name2[getRandomInt(0, name2.length + 1)]);
  return name.trim();
}

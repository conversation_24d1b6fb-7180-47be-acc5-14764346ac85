import { Alert, Platform } from 'react-native';
import {
  initConnection,
  purchaseErrorListener,
  purchaseUpdatedListener,
  finishTransaction,
  requestPurchase,
  getSubscriptions,
  requestSubscription,
  getProducts,
  getAvailablePurchases,
  PurchaseError,
} from 'react-native-iap';
import { IAP_SKUS } from '../Constants/iapConstants';
import backendIAPService from './backendIAPService';

class IAPService {
  constructor() {
    this.lastErrorTimestamp = 0;
    this.ERROR_DEBOUNCE_MS = 1000; // 1 second debounce
    this.purchaseUpdateSubscription = null;
    this.purchaseErrorSubscription = null;
    this.onPurchaseComplete = null;
  }

  async init() {
    try {
      console.log('Initializing IAP...');

      await initConnection();

      // Check for and consume any existing purchases that might be stuck
      await this.checkAndConsumeExistingPurchases();

      await this.isAdFree(); // Check if the user already owns the remove ads feature
      this.setupListeners();
    } catch (error) {
      const timestamp = new Date().toISOString();
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      console.error(`Error initializing IAP at ${timestamp}:`, error);
    }
  }

  getSubscriptionExpiryDate(purchase) {
    if (Platform.OS === 'ios') {
      // iOS: Use original transaction date + subscription period
      return purchase.originalTransactionDateIOS
        ? new Date(
          purchase.originalTransactionDateIOS + 30 * 24 * 60 * 60 * 1000,
        )
        : null;
    } else {
      // Android: Use subscription expiry date
      return purchase.subscriptionPeriodAndroid
        ? new Date(
          parseInt(purchase.purchaseTimeMillis) +
          parseInt(purchase.subscriptionPeriodAndroid),
        )
        : null;
    }
  }

  isSubscriptionActive(purchase) {
    if (!purchase) return false;

    const expiryDate = this.getSubscriptionExpiryDate(purchase);
    if (!expiryDate) return false;

    return expiryDate > new Date();
  }

  async isAdFree() {
    try {
      console.log('Checking ad-free status...');

      // First try to get status from backend
      const backendStatus = await backendIAPService.getUserStatus();

      if (backendStatus.success) {
        console.log('Got IAP status from backend:', backendStatus.status);
        return backendStatus.status.hasAdFreeAccess;
      } else {
        console.log('Backend status check failed, falling back to local validation');
      }

      // Fallback to local validation if backend is unavailable
      console.log('Getting available purchases for local validation...');
      const purchases = await getAvailablePurchases();
      console.log(`Found ${purchases.length} available purchases:`, purchases.map(p => ({
        productId: p.productId,
        transactionId: p.transactionId,
        purchaseState: p.purchaseState
      })));

      // Check subscription status
      const subscription = purchases.find(
        purchase => purchase.productId === IAP_SKUS.REMOVE_ADS_Subscription,
      );

      if (subscription) {
        const subscriptionExpiryDate =
          this.getSubscriptionExpiryDate(subscription);
        console.log(`Subscription found, expiry date: ${subscriptionExpiryDate}`);
        if (subscriptionExpiryDate && subscriptionExpiryDate > new Date()) {
          console.log('Subscription is active, returning true');
          return true;
        } else {
          console.log('Subscription is expired or invalid expiry date');
        }
      } else {
        console.log('No active subscription found');
      }

      // Check one-time purchase status
      const oneMonthPurchase = purchases.find(
        purchase => purchase.productId === IAP_SKUS.ONE_MONTH_AD_FREE,
      );

      if (oneMonthPurchase) {
        console.log('One month purchase found:', {
          transactionId: oneMonthPurchase.transactionId,
          purchaseState: oneMonthPurchase.purchaseState
        });

        try {
          const receipt = JSON.parse(oneMonthPurchase.transactionReceipt);
          const expiryDate = new Date(
            receipt.purchaseTime + 30 * 24 * 60 * 60 * 1000,
          );
          console.log(
            `One month ad free expiry date: ${expiryDate}, isActive: ${expiryDate > new Date()}`,
          );

          const isActive = expiryDate > new Date();
          if (isActive) {
            console.log('One month purchase is active, returning true');
          } else {
            console.log('One month purchase is expired, attempting to consume it...');

            // If purchase is expired, consume it to clean up
            try {
              await finishTransaction({
                purchase: oneMonthPurchase,
                isConsumable: true,
              });
              console.log('Expired purchase consumed successfully');
            } catch (consumeError) {
              console.error('Error consuming expired purchase:', consumeError);
            }
          }
          return isActive;
        } catch (parseError) {
          console.error('Error parsing receipt:', parseError);
          return false;
        }
      } else {
        console.log('No one month purchase found');
      }

      console.log('No active purchases found, returning false');
      return false;
    } catch (error) {
      console.error('Error checking available purchases:', error);
      return false;
    }
  }

  /**
   * Sync IAP status with backend
   * @returns {Promise<Object>} Backend status or null if failed
   */
  async syncWithBackend() {
    try {
      console.log('Syncing IAP status with backend...');
      const backendStatus = await backendIAPService.refreshUserStatus();

      if (backendStatus.success) {
        console.log('Backend sync successful:', backendStatus.status);
        return backendStatus.status;
      } else {
        console.error('Backend sync failed:', backendStatus.error);
        return null;
      }
    } catch (error) {
      console.error('Error syncing with backend:', error);
      return null;
    }
  }

  setupListeners() {
    if (this.purchaseUpdateSubscription) {
      this.purchaseUpdateSubscription.remove();
    }
    if (this.purchaseErrorSubscription) {
      this.purchaseErrorSubscription.remove();
    }

    this.purchaseUpdateSubscription = purchaseUpdatedListener(
      async purchase => {
        if (purchase) {
          const timestamp = new Date().toISOString();
          console.log(`Purchase updated at ${timestamp}:`, {
            productId: purchase.productId,
            transactionId: purchase.transactionId,
            purchaseState: purchase.purchaseState,
            purchaseTime: purchase.purchaseTime,
            platform: Platform.OS
          });

          try {
            console.log('Starting backend verification...');
            // First verify with backend
            const backendVerification = await backendIAPService.verifyPurchase(purchase);

            if (backendVerification.success) {
              console.log('Backend verification successful:', {
                purchaseStatus: backendVerification.purchase?.status,
                userStatus: backendVerification.userStatus
              });

              // Finish the transaction
              console.log('Finishing transaction...');
              await finishTransaction({
                purchase,
                isConsumable: true,
              });
              console.log('Transaction finished successfully');

              // Notify success through callback if provided
              if (this.onPurchaseComplete) {
                const purchaseStatus = true; // Purchase was successful
                this.onPurchaseComplete(purchase.productId, purchaseStatus, backendVerification.userStatus);
              }
            } else {
              console.error('Backend verification failed:', backendVerification.error);

              // Still finish the transaction to avoid issues, but notify of verification failure
              console.log('Finishing transaction despite verification failure...');
              await finishTransaction({
                purchase,
                isConsumable: true,
              });
              console.log('Transaction finished (verification failed)');

              if (this.onPurchaseComplete) {
                this.onPurchaseComplete(purchase.productId, false, null);
              }

              Alert.alert(
                'Verification Error',
                'Purchase completed but verification failed. Please contact support if you don\'t receive your benefits.'
              );
            }
          } catch (error) {
            const errorTimestamp = new Date().toISOString();
            const errorMessage = error.message || 'Unknown error occurred';
            const errorDetails = {
              code: error.code,
              debugMessage: error.debugMessage,
              responseCode: error.responseCode,
            };
            console.error(
              `Error processing purchase at ${errorTimestamp}:`,
              Object.assign({ message: errorMessage }, errorDetails),
            );

            // Try to finish transaction even if verification failed
            try {
              console.log('Attempting to finish transaction after error...');
              await finishTransaction({
                purchase,
                isConsumable: true,
              });
              console.log('Transaction finished after error');
            } catch (finishError) {
              console.error('Error finishing transaction:', finishError);
            }
          }
        }
      },
    );

    this.purchaseErrorSubscription = purchaseErrorListener(error => {
      const currentTime = Date.now();
      // Debounce errors that occur too quickly
      if (currentTime - this.lastErrorTimestamp < this.ERROR_DEBOUNCE_MS) {
        return;
      }
      this.lastErrorTimestamp = currentTime;
      const timestamp = new Date().toISOString();
      const errorMessage = error.message || 'Unknown error occurred';
      const errorDetails = {
        code: error.code,
        debugMessage: error.debugMessage,
        responseCode: error.responseCode,
      };
      console.error(
        `Purchase error at ${timestamp}:`,
        Object.assign({ message: errorMessage }, errorDetails),
      );
    });
  }

  async getRemoveAdsProduct() {
    try {
      const products = await getSubscriptions({
        skus: [IAP_SKUS.REMOVE_ADS_Subscription],
      });
      return products[0];
    } catch (error) {
      console.error('Error getting remove ads product:', error);
      return null;
    }
  }

  async subscribeRemoveAds(product, onComplete) {
    try {
      this.onPurchaseComplete = onComplete;
      await requestSubscription({
        sku: IAP_SKUS.REMOVE_ADS_Subscription,
        ...(product.subscriptionOfferDetails && {
          subscriptionOffers: [
            {
              sku: IAP_SKUS.REMOVE_ADS_Subscription,
              offerToken: product.subscriptionOfferDetails[0].offerToken,
            },
          ],
        }),
        andDangerouslyFinishTransactionAutomaticallyIOS: false,
      });
      return true;
    } catch (error) {
      if (error instanceof PurchaseError) {
        if (error.code !== 'E_USER_CANCELLED') {
          Alert.alert('Purchase Error', error.message);
        }
      } else {
        Alert.alert('Error', 'Failed to complete purchase. Please try again.');
      }
      console.error('Error purchasing remove ads:', error);
      return false;
    }
  }

  async getAdFreeOneMonthProduct() {
    try {
      const products = await getProducts({
        skus: [IAP_SKUS.ONE_MONTH_AD_FREE],
      });
      return products[0];
    } catch (error) {
      console.error('Error getting one month product:', error);
      return null;
    }
  }

  async purchaseAdFreeOneMonth(onComplete) {
    try {
      this.onPurchaseComplete = onComplete;
      console.log('Starting one month ad-free purchase process...');

      await requestPurchase({
        skus: [IAP_SKUS.ONE_MONTH_AD_FREE],
        andDangerouslyFinishTransactionAutomaticallyIOS: false,
      });
      return true;
    } catch (error) {
      const timestamp = new Date().toISOString();
      const errorDetails = {
        code: error.code,
        message: error.message,
        debugMessage: error.debugMessage,
        responseCode: error.responseCode,
      };

      console.error(`Error purchasing one month ad free at ${timestamp}:`, errorDetails);

      // Handle "already owned" error specifically
      if (error.code === 'E_ALREADY_OWNED') {
        console.log('Purchase already owned, attempting to consume and restore...');

        // Try to get available purchases and consume this one
        try {
          const purchases = await getAvailablePurchases();
          const oneMonthPurchase = purchases.find(
            p => p.productId === IAP_SKUS.ONE_MONTH_AD_FREE
          );

          if (oneMonthPurchase) {
            console.log('Found existing purchase, attempting to finish transaction:', oneMonthPurchase);

            // Finish the transaction to consume it
            await finishTransaction({
              purchase: oneMonthPurchase,
              isConsumable: true,
            });

            console.log('Successfully consumed existing purchase');

            // Try to verify with backend
            const backendVerification = await backendIAPService.verifyPurchase(oneMonthPurchase);

            if (backendVerification.success) {
              console.log('Backend verification successful after consuming existing purchase');
              if (this.onPurchaseComplete) {
                this.onPurchaseComplete(oneMonthPurchase.productId, true, backendVerification.userStatus);
              }
              return true;
            } else {
              console.error('Backend verification failed after consuming purchase:', backendVerification.error);
            }
          }
        } catch (consumeError) {
          console.error('Error consuming existing purchase:', consumeError);
        }

        Alert.alert(
          'Purchase Issue',
          'You already own this item. The system is attempting to restore your purchase. Please wait a moment.'
        );
      } else if (error instanceof PurchaseError) {
        if (error.code !== 'E_USER_CANCELLED') {
          Alert.alert('Purchase Error', error.message);
        }
      } else {
        Alert.alert('Error', 'Failed to complete purchase. Please try again.');
      }

      return false;
    }
  }

  /**
   * Check for existing purchases and consume them if they're not processed
   * This handles cases where purchases exist but weren't properly consumed
   */
  async checkAndConsumeExistingPurchases() {
    try {
      console.log('Checking for existing purchases that need consumption...');
      const purchases = await getAvailablePurchases();

      const oneMonthPurchases = purchases.filter(
        p => p.productId === IAP_SKUS.ONE_MONTH_AD_FREE
      );

      console.log(`Found ${oneMonthPurchases.length} one-month purchases to check`);

      for (const purchase of oneMonthPurchases) {
        try {
          console.log(`Processing existing purchase: ${purchase.transactionId}`);

          // Verify with backend first
          const backendVerification = await backendIAPService.verifyPurchase(purchase);

          if (backendVerification.success) {
            console.log('Backend verification successful for existing purchase');

            // Finish the transaction to consume it
            await finishTransaction({
              purchase,
              isConsumable: true,
            });

            console.log('Successfully consumed existing purchase');

            // Notify callback if available
            if (this.onPurchaseComplete) {
              this.onPurchaseComplete(purchase.productId, true, backendVerification.userStatus);
            }
          } else {
            console.log('Backend verification failed for existing purchase, consuming anyway');

            // Still consume the purchase to clear it from the queue
            await finishTransaction({
              purchase,
              isConsumable: true,
            });

            console.log('Consumed purchase despite verification failure');
          }
        } catch (purchaseError) {
          console.error('Error processing existing purchase:', purchaseError);
        }
      }

      return oneMonthPurchases.length > 0;
    } catch (error) {
      console.error('Error checking for existing purchases:', error);
      return false;
    }
  }

  cleanup() {
    if (this.purchaseUpdateSubscription) {
      this.purchaseUpdateSubscription.remove();
    }
    if (this.purchaseErrorSubscription) {
      this.purchaseErrorSubscription.remove();
    }
  }
}

export const iapService = new IAPService();

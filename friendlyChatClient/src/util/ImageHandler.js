import SocketService from '../services/socketIoService';

const pieces = 4;
const percent = 25;

let pendingImages = [];
let isSendingImage = false;
export function sendImage(ext, base64, id, callback) {
  if (isSendingImage) {
    pendingImages.push({base64: base64, id: id, ext: ext});
  } else {
    isSendingImage = true;
    let step = Number.parseInt(base64.length / pieces, 10);
    sendPiece(step, 0, {base64, id, ext}, callback);
  }
}
function sendPiece(step, i, {base64, id, ext}, updateMessage) {
  let element = getPiece(step, i, base64);
  let data = {
    data: element,
    index: i,
    id: id,
  };
  SocketService.emit('image', data, part => {
    updateMessage(id, 'progress', Number.parseInt(++part * percent, 10));
    if (part < pieces) {
      sendPiece(step, part, {base64, id, ext}, updateMessage);
    } else {
      SocketService.emit('imageSent', {id, ext, pieces}, ackMessage => {
        if (pendingImages.length > 0) {
          let msg = pendingImages.shift();
          step = Number.parseInt(msg.base64.length / pieces, 10);
          sendPiece(step, 0, msg, updateMessage);
        } else {
          isSendingImage = false;
        }
      });
    }
  });
}

function getPiece(step, i, base64) {
  let start = step * i;
  let end = i === pieces - 1 ? base64.length : step * (i + 1);
  // //console.log(i, pieces - 1, start, end);
  let element = base64.slice(start, end);
  return element;
}

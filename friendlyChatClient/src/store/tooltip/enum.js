export class Stage {
  // Create new instances of the same class as static attributes
  static Avatar = new Stage('setYourAvatar');
  static Camera = new Stage('camera');
  static Galary = new Stage('galary');
  static Menu = new Stage('menu');
  static History = new Stage('history');
  static HistoryList = new Stage('historyList');
  static Settings = new Stage('settings');
  static End = new Stage('end');
  static Empty = new Stage('empty');

  constructor(name) {
    this.name = name;
  }
}

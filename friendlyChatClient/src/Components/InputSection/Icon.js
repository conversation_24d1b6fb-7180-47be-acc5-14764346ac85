import {StyleSheet, Text, TouchableOpacity} from 'react-native';
import React from 'react';
import {useRecoilState} from 'recoil';
import {TooltipState} from '../../store/tooltip/atoms';
import Tooltip from 'react-native-walkthrough-tooltip';

export default function Icon({
  render,
  attachIconStyle,
  onPress,
  stage,
  nextStage,
  children,
  tooltipText,
}) {
  const [toolTipStage, setToolTipStage] = useRecoilState(TooltipState);

  return (
    render &&
    (stage ? (
      <Tooltip
        isVisible={toolTipStage === stage}
        content={<Text style={styles.text}>{tooltipText}</Text>}
        placement="top"
        accessible={false}
        displayInsets={{top: 0, bottom: 0, left: 0, right: 0}}
        useInteractionManager={true}
        onClose={() => setToolTipStage(nextStage)}>
        <TouchableOpacity
          style={[styles.iconWrapper, attachIconStyle]}
          onPress={onPress}
          activeOpacity={0.6}
          hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}
          delayPressIn={0}>
          {children}
        </TouchableOpacity>
      </Tooltip>
    ) : (
      <TouchableOpacity
        style={[styles.iconWrapper, attachIconStyle]}
        onPress={onPress}
        activeOpacity={0.6}
        hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}
        delayPressIn={0}>
        {children}
      </TouchableOpacity>
    ))
  );
}

const styles = StyleSheet.create({
  iconWrapper: {paddingHorizontal: 3},
  text: {color: 'black'},
});

import React, {useCallback, useMemo} from 'react';
import {StyleSheet, View, Alert, Platform} from 'react-native';
import {PressableOpacity} from 'react-native-pressable-opacity';
import RNFS from 'react-native-fs';

export default function CaptureButton({
  camera,
  onMediaCaptured,
  style,
  cameraPosition,
}) {
  const capturePhoto = useCallback(async () => {
    try {
      if (!camera?.current) return;
  
      const photoDir = `${RNFS.CachesDirectoryPath}/photos`;
      await RNFS.mkdir(photoDir);
  
      const photo = await camera.current.takePhoto({
        qualityPrioritization: 'quality',
        flash: 'off',
        enableAutoRedEyeReduction: true,
        photoCodec: 'jpeg',
        quality: 95,
        enableShutterSound: false
      });
  
      const photoPath = Platform.OS === 'android' ? `file://${photo.path}` : photo.path;
      onMediaCaptured({...photo, path: photoPath}, 'photo');
    } catch (e) {
      console.error('Failed to capture photo:', e);
      Alert.alert('Error', e.message);
    }
  }, [camera, onMediaCaptured]);
  const buttonStyle = useMemo(() => ({...styles.button, ...style}), [style]);

  return (
    <View style={buttonStyle}>
      <PressableOpacity
        style={styles.pressable}
        onPress={capturePhoto}
        disabledOpacity={0.4}>
        <View style={styles.innerButton} />
      </PressableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  button: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(140, 140, 140, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  pressable: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
  },
  innerButton: {
    width: 66,
    height: 66,
    borderRadius: 33,
    borderWidth: 2,
    borderColor: 'black',
  },
});

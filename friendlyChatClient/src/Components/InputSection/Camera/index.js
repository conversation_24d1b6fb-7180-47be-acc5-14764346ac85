// index.js
import React, { useCallback, useState, useRef, useEffect } from 'react';
import { StyleSheet, View, StatusBar, Text } from 'react-native';
import { Camera, useCameraDevices } from 'react-native-vision-camera';
import { PressableOpacity } from 'react-native-pressable-opacity';
import IonIcon from 'react-native-vector-icons/Ionicons';
import { SAFE_AREA_PADDING } from '../../../Constants/Dimentions';
import { useIsForeground } from './hooks/useIsForeground';
import CaptureButton from './views/CaptureButton';
import Reanimated from 'react-native-reanimated';

export default function CameraPage({ navigation }) {
  const camera = useRef(null);
  const [cameraPermission, setCameraPermission] = useState();
  const [flash, setFlash] = useState('off');
  const [cameraPosition, setCameraPosition] = useState('back');
  const [isInitializing, setIsInitializing] = useState(true);
  const [cameraError, setCameraError] = useState(null);

  const devices = useCameraDevices();
  const device = React.useMemo(() => {
    if (!devices) return undefined;

    // Get all available devices
    const deviceList = Object.values(devices);
    if (deviceList.length === 0) return undefined;

    // Find device by position
    const targetDevice = deviceList.find(d =>
      d.position === cameraPosition
    );

    // Fallback to first available device if specific position not found
    return targetDevice || deviceList[0];
  }, [devices, cameraPosition]);

  const isForeground = useIsForeground();
  const isActive =
    isForeground &&
    (cameraPermission === 'authorized' || cameraPermission === 'granted');

  useEffect(() => {
    let mounted = true;

    Camera.requestCameraPermission()
      .then(permission => {
        if (mounted) {
          setCameraPermission(permission);
        }
      })
      .finally(() => {
        if (mounted) {
          setIsInitializing(false);
        }
      });

    // Cleanup function
    return () => {
      mounted = false;

      // Ensure we cleanup all resources
      if (camera.current) {
        camera.current = null;
      }
    };
  }, [navigation]);

  // Additional debug logging for device changes
  React.useEffect(() => {
    console.log('Current camera state:', {
      devices: devices ? Object.keys(devices) : 'null',
      selectedPosition: cameraPosition,
      currentDevice: device?.id,
      permission: cameraPermission,
      isActive,
    });

    // Detailed device info for debugging
    if (devices) {
      Object.entries(devices).forEach(([key, camDevice]) => {
        console.log(`Device ${key}:`, {
          id: camDevice.id,
          position: camDevice.position,
          name: camDevice.name,
          hasFlash: camDevice.hasFlash,
          formatsCount: camDevice.formats?.length,
          supportsPhotoHDR: camDevice.supportsPhotoHDR,
          supportsVideoHDR: camDevice.supportsVideoHDR,
          hardwareLevel: camDevice.hardwareLevel,
        });

        // Log available formats for debugging
        if (camDevice.formats) {
          camDevice.formats.forEach((format, index) => {
            console.log(`  Format ${index}:`, {
              videoStabilizationModes: format.videoStabilizationModes,
              autoFocusSystem: format.autoFocusSystem,
              photoHeight: format.photoHeight,
              photoWidth: format.photoWidth,
              videoHeight: format.videoHeight,
              videoWidth: format.videoWidth,
              maxISO: format.maxISO,
              minISO: format.minISO,
            });
          });
        }
      });
    }
  }, [devices, device, cameraPosition, cameraPermission, isActive]);

  const onFlashPressed = useCallback(() => {
    setFlash(f => (f === 'off' ? 'on' : 'off'));
  }, []);

  const onFlipPressed = useCallback(() => {
    setCameraPosition(p => (p === 'back' ? 'front' : 'back'));
  }, []);
  const onFocusTap = useCallback(
    ({ nativeEvent: event }) => {
      if (!device?.supportsFocus) return;
      camera.current?.focus({
        x: event.locationX,
        y: event.locationY,
      });
    },
    [device?.supportsFocus],
  );

  const onMediaCaptured = useCallback(
    async (media, type) => {
      try {
        if (!media?.path) {
          console.error('No media path available');
          return;
        }
        navigation.navigate('MediaPage', {
          path: media.path,
          type: type,
          isCam: true,
        });
      } catch (error) {
        console.error('Media capture error:', error);
      }
    },
    [navigation],
  );

  if (isInitializing) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <IonIcon name="hourglass-outline" color="white" size={40} />
        <Text style={styles.errorText}>Initializing camera...</Text>
      </View>
    );
  }

  if (!device) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <IonIcon name="camera-outline" color="white" size={40} />
        <Text style={styles.errorText}>Camera not available</Text>
        <Text style={[styles.errorText, styles.smallText]}>
          Permission: {cameraPermission || 'unknown'}
        </Text>
        <Text style={[styles.errorText, styles.smallText]}>
          Available devices:{' '}
          {devices ? Object.keys(devices).join(', ') : 'none'}
        </Text>
      </View>
    );
  }

  // Show error screen if camera failed to configure
  if (cameraError) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <IonIcon name="warning-outline" color="white" size={40} />
        <Text style={styles.errorText}>Camera Configuration Error</Text>
        <Text style={[styles.errorText, styles.smallText]}>
          {cameraError.message}
        </Text>
        <Text style={[styles.errorText, styles.smallText]}>
          Code: {cameraError.code}
        </Text>
        <PressableOpacity
          style={[styles.button, { flexDirection: 'row', marginTop: 20 }]}
          onPress={() => setCameraError(null)}
        >
          <IonIcon name="refresh" color="white" size={24} />
          <Text style={[styles.errorText, { marginLeft: 10, color: 'white' }]}>Try Again</Text>
        </PressableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar translucent backgroundColor="transparent" />
      <Reanimated.View onTouchEnd={onFocusTap} style={styles.camera}>
        <Camera
          enableHighQualityPhotos={true}
          ref={camera}
          style={styles.camera}
          device={device}
          isActive={isActive}
          photo={true}
          video={false}
          audio={false}
          enableZoomGesture
          flash={flash}
          orientation="portrait"
          pixelFormat="yuv"
          onError={error => {
            console.error('Camera error:', error);
            console.log('Camera error details:', {
              code: error.code,
              message: error.message,
              cause: error.cause ? error.cause.toString() : 'No cause',
            });

            // Handle specific camera configuration errors
            if (error.code === 'session/invalid-output-configuration') {
              console.error('Camera configuration error detected. This may be due to:');
              console.error('- Missing camera hardware features in AndroidManifest.xml');
              console.error('- Incompatible camera device configuration');
              console.error('- Unsupported camera formats or resolutions');

              // Try to get more device info for debugging
              if (device) {
                console.log('Current device info:', {
                  id: device.id,
                  position: device.position,
                  hasFlash: device.hasFlash,
                  formats: device.formats?.length,
                  supportsPhotoHDR: device.supportsPhotoHDR,
                  supportsVideoHDR: device.supportsVideoHDR,
                });
              }
            }
          }}
        />
      </Reanimated.View>

      <View style={styles.rightButtonRow}>
        <PressableOpacity style={styles.button} onPress={onFlashPressed}>
          <IonIcon
            name={flash === 'on' ? 'flash' : 'flash-off'}
            color="white"
            size={24}
          />
        </PressableOpacity>
        <PressableOpacity style={styles.button} onPress={onFlipPressed}>
          <IonIcon name="camera-reverse" color="white" size={24} />
        </PressableOpacity>
      </View>

      <View style={styles.bottomButtonRow}>
        <PressableOpacity
          style={styles.closeButton}
          onPress={navigation.goBack}>
          <IonIcon name="close" size={32} color="white" style={styles.icon} />
        </PressableOpacity>

        <CaptureButton
          style={styles.captureButton}
          camera={camera}
          onMediaCaptured={onMediaCaptured}
          cameraPosition={cameraPosition}
        />

        <View style={styles.closeButton} />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
  cameraContainer: {
    flex: 1,
    position: 'relative',
  },
  camera: {
    flex: 1,
  },
  focusOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'transparent',
    pointerEvents: 'box-none',
  },
  rightButtonRow: {
    position: 'absolute',
    right: SAFE_AREA_PADDING.paddingRight,
    top: SAFE_AREA_PADDING.paddingTop,
  },
  button: {
    marginBottom: 20,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(140, 140, 140, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  bottomButtonRow: {
    position: 'absolute',
    bottom: SAFE_AREA_PADDING.paddingBottom,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 30,
    width: '100%',
  },
  closeButton: {
    width: 45,
    height: 45,
    justifyContent: 'center',
    alignItems: 'center',
  },
  captureButton: {
    position: 'relative',
    alignSelf: 'center',
  },
  icon: {
    textShadowColor: 'black',
    textShadowOffset: {
      height: 0,
      width: 0,
    },
    textShadowRadius: 1,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    color: 'white',
    fontSize: 16,
    marginTop: 10,
    textAlign: 'center',
  },
  smallText: {
    fontSize: 12,
    opacity: 0.7,
    marginTop: 5,
  },
});

import React from 'react';
import { StyleSheet, TouchableOpacity, Text, Platform } from 'react-native';
import { useTheme } from 'react-native-paper';
import { useRecoilValue } from 'recoil';
import { sessionState } from '../../store/session/atoms';
import { useIsAdFree } from '../../contexts/IAPContext';
import { reconnectWithAdLogic } from '../../utils/reconnectUtils';

export default function ReconnectButton() {

  const theme = useTheme();
  const styles = makeStyles(theme);
  const session = useRecoilValue(sessionState);
  const isAdFree = useIsAdFree(); // Use optimized cached ad-free check

  const reconnect = () => {
    reconnectWithAdLogic(session.partners, isAdFree);
  };
  return (
    <TouchableOpacity style={styles.btn} onPress={reconnect}>
      <Text style={styles.txt}>Find new friend</Text>
    </TouchableOpacity>
  );
}
const makeStyles = (theme) => StyleSheet.create({
  btn: {
    backgroundColor: theme.colors.primary,
    alignSelf: 'center',
    height: 40,
    justifyContent: 'center',
    paddingHorizontal: 10,
    marginVertical: 10,
    borderRadius: 12,
  },
  txt: {
    color: theme.colors.onPrimary,
    textAlignVertical: 'center',
    fontFamily:
      Platform.OS === 'android' ? 'PoppinsRegular-B2Bw' : 'Poppins-Regular',
  },
});

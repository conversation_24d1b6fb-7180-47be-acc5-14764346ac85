import React from 'react';
import {
  StyleSheet,
  View,
  TextInput,
  TouchableOpacity,
  Platform,
  Keyboard
} from 'react-native';
import { useTheme } from 'react-native-paper';
import ActionInputMenu from './Messages/Menu';
import Camera from '../assets/images/camera.svg';
import Photos from '../assets/images/photos.svg';
import Send from '../assets/images/send.svg';
import Smile from '../assets/images/smiley-svgrepo-com.svg';
import OpenMenu from '../assets/images/openMenu.svg';
import CloseMenu from '../assets/images/closeMenu.svg';
import { Stage } from '../store/tooltip/enum';
import Icon from './InputSection/Icon';
import analytics from '@react-native-firebase/analytics';
import { IS_LITE_VERSION } from '../config/buildConfig';

export default function UserInput(props) {
  const theme = useTheme();
  const styles = createStyles(theme);

  const sendText = () => {
    if (props.value) {
      // Call onSubmit immediately for better responsiveness
      props.onSubmit();

      // Log analytics event after UI update
      setTimeout(() => {
        analytics().logEvent('send_message');
      }, 0);
    }
  };

  const onEmojiIconClicked = () => {
    if (!props.showEmoji) {
      Keyboard.dismiss();
    }
    props.setShowEmoji(!props.showEmoji);
  };

  const menuAnchor = visible => {
    return visible ? (
      <CloseMenu fill={theme.colors.icon} width={25} height={25} />
    ) : (
      <OpenMenu fill={theme.colors.icon} width={25} height={25} />
    );
  };


  return (
    <View style={styles.container}>
      <View style={styles.iconWrapper}>
        <ActionInputMenu
          menuAnchor={menuAnchor}
          menuItems={props.menuItems}
        />
      </View>
      <Icon
        render={!props.value}
        onPress={props.onGaralryPick}
        stage={Stage.Galary}
        nextStage={Stage.Menu}
        tooltipText="Click to choose a photo from galary"
        style={styles.iconSpacing}>
        <Photos fill={theme.colors.icon} width={20} height={20} />
      </Icon>
      <Icon
        render={!props.value}
        onPress={props.onCameraOpen}
        stage={Stage.Camera}
        nextStage={Stage.Galary}
        tooltipText="Click to send a photo using your camera"
        style={styles.iconSpacing}>
        <Camera fill={theme.colors.icon} width={20} height={20} />
      </Icon>
      {!IS_LITE_VERSION && (
        <Icon
          render
          onPress={onEmojiIconClicked}
          attachIconStyle={styles.emojiIcon}>
          <Smile fill={theme.colors.icon} width={20} height={20} />
        </Icon>
      )}
      <View style={styles.inputWrapper}>
        <TextInput
          style={[styles.input]}
          placeholderTextColor={theme.colors.placeholder}
          placeholder={props.placeholder}
          returnKeyType={props.returnKeyType}
          onChangeText={props.onChangeText}
          multiline={true}
          maxLength={1000} // Limit text length for better performance
          autoCapitalize="sentences"
          autoCorrect={true}
          onSelectionChange={event =>
            event.nativeEvent.selection.end ===
            event.nativeEvent.selection.start &&
            props.setCursorPosition(event.nativeEvent.selection.start)
          }
          value={props.value}
          onSubmitEditing={sendText}
          onFocus={() => {
            props.setShowEmoji(false);
          }}
        />
        {props.value !== '' && (
          <TouchableOpacity
            style={styles.sendButton}
            onPress={sendText}
            activeOpacity={0.6}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            delayPressIn={0}>
            <View style={styles.sendButtonInner}>
              <Send fill={theme.colors.onPrimary} width={20} height={20} />
            </View>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
}

// Create dynamic styles based on theme
const createStyles = (theme) => StyleSheet.create({
  input: {
    flex: 1,
    fontFamily:
      Platform.OS === 'ios' ? 'Poppins-Regular' : 'PoppinsRegular-B2Bw',
    backgroundColor: theme.colors.textInputBackground,
    borderRadius: 20,
    fontSize: 17,
    paddingRight: 50,
    paddingLeft: 15,
    marginRight: 5,
    color: theme.colors.inputText,
  },
  inputWrapper: {
    flex: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  container: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 5,
    marginBottom: 10,

  },
  sendButton: {
    zIndex: 99,
    position: 'absolute',
    right: 15,
    height: 36,
    width: 36,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonInner: {
    width: 36,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.primary,
    borderRadius: 18,
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 2,
  },
  emojiIcon: { paddingRight: 10 },
  iconWrapper: { paddingHorizontal: 10 },
  iconSpacing: { paddingHorizontal: 10 },
});

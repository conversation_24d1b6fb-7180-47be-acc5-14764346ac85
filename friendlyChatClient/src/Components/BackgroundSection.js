import React from 'react';
import {
  StyleSheet,
  ImageBackground,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from 'react-native-paper';
const BackgroundSection = props => {
  const theme = useTheme();
  const styles = makeStyles(theme);


  return (
    <ImageBackground
      source={require('../assets/images/background.png')}
      imageStyle={styles.imageStyle}
      style={styles.container}>
      <SafeAreaView style={styles.container}>
        {props.children}
      </SafeAreaView>
    </ImageBackground >
  );
};
const makeStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    paddingBottom: 5,
    color: 'red',
  },
  imageStyle: {
    opacity: theme.dark ? 0.3 : 0.5
    , backgroundColor: theme.colors.backgroundColor,

  },
});
export default BackgroundSection;

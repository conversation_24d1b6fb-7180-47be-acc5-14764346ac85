import React, { useRef, useEffect } from 'react';
import { Animated, StyleSheet, View } from 'react-native';
import { FlashList } from "@shopify/flash-list";
import { useRecoilValue } from 'recoil';
import { messageSelector } from '../store/session/selectors';
import ChatMessage from './ChatMessage';

const ChattingSection = props => {
  const messages = useRecoilValue(messageSelector);
  const fadeAnim = useRef(new Animated.Value(0)).current;

  // Stable key extractor to prevent remounting issues
  const keyExtractor = React.useCallback((item, index) => {
    return item.id ? item.id.toString() : `message-${index}`;
  }, []);

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, [fadeAnim]);

  const renderItem = ({ item, index }) => {
    // Simplified rendering without per-item animations to prevent mounting conflicts
    const messageComponent = item.source ? (
      <ChatMessage item={item} />
    ) : (
      <ChatMessage
        item={{ ...item, name: item.name, gender: item.gender, avatar: item.avatar }}
      />
    );

    return (
      <Animated.View style={{ opacity: fadeAnim }}>
        {messageComponent}
      </Animated.View>
    );
  };

  return (
    <View style={[props.style, styles.container]}>
      <FlashList
        maintainVisibleContentPosition={{
          autoscrollToTopThreshold: 10,
          autoscrollToBottomThreshold: 0.2,
          startRenderingFromBottom: true,
          minIndexForVisible: 0,
        }}
        data={messages}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        contentContainerStyle={styles.listContent}
        overrideProps={{ contentContainerStyle: styles.override }}
        showsVerticalScrollIndicator={false}
        initialNumToRender={15}
        maxToRenderPerBatch={10}
        windowSize={10}
        removeClippedSubviews={false}
        getItemLayout={null}

      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  listContent: {
    paddingVertical: 16,
    paddingHorizontal: 8,
  },
  override: {
    flexGrow: 1,
    paddingHorizontal: 8,
    justifyContent: 'flex-end'
  },
});

export default ChattingSection;

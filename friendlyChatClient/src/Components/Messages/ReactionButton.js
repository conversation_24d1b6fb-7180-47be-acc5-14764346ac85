import React, { useRef, useEffect } from 'react';
import { TouchableOpacity, Text, Animated, StyleSheet } from 'react-native';
import { useTheme } from 'react-native-paper';

export default function ReactionButton({ 
  emoji, 
  count, 
  isActive, 
  onPress, 
  reactionType 
}) {
  const theme = useTheme();
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const countAnim = useRef(new Animated.Value(1)).current;
  const prevCount = useRef(count);
  
  const styles = createStyles(theme);

  // Animate button press
  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.95,
      useNativeDriver: true,
      tension: 300,
      friction: 10,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
      tension: 300,
      friction: 10,
    }).start();
  };

  // Animate count changes
  useEffect(() => {
    if (prevCount.current !== count) {
      Animated.sequence([
        Animated.timing(countAnim, {
          toValue: 1.2,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(countAnim, {
          toValue: 1,
          duration: 150,
          useNativeDriver: true,
        }),
      ]).start();
      prevCount.current = count;
    }
  }, [count, countAnim]);

  return (
    <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
      <TouchableOpacity
        style={[
          styles.button,
          isActive && styles.buttonActive
        ]}
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={0.8}
        hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
      >
        <Text style={styles.emoji}>{emoji}</Text>
        <Animated.View style={{ transform: [{ scale: countAnim }] }}>
          <Text style={[
            styles.count,
            isActive && styles.countActive
          ]}>
            {count}
          </Text>
        </Animated.View>
      </TouchableOpacity>
    </Animated.View>
  );
}

const createStyles = (theme) => StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    paddingHorizontal: 10,
    paddingVertical: 6,
    marginRight: 6,
    marginBottom: 4,
    borderWidth: 1,
    borderColor: theme.colors.outline,
    minHeight: 32,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  buttonActive: {
    backgroundColor: theme.colors.primaryContainer,
    borderColor: theme.colors.primary,
    borderWidth: 1.5,
  },
  emoji: {
    fontSize: 16,
    marginRight: 6,
  },
  count: {
    fontSize: 13,
    fontWeight: '600',
    color: theme.colors.onSurface,
    minWidth: 14,
    textAlign: 'center',
  },
  countActive: {
    color: theme.colors.primary,
    fontWeight: '700',
  },
});

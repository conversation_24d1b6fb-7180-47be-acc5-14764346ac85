import React from 'react';
import { View, StyleSheet, TouchableOpacity, Text, Animated } from 'react-native';
import { useTheme } from 'react-native-paper';
import { useRecoilValue } from 'recoil';
import { messageReactionsSelector, messageReactionCountsSelector } from '../../store/session/selectors';

// Reaction emoji mapping
const REACTION_EMOJIS = {
  like: '👍',
  heart: '❤️',
  laugh: '😂',
  wow: '😮'
};

const REACTION_ORDER = ['like', 'heart', 'laugh', 'wow'];

export default function MessageReactions({ messageId, onReactionPress, currentUserId }) {
  const theme = useTheme();
  const reactions = useRecoilValue(messageReactionsSelector(messageId));
  const reactionCounts = useRecoilValue(messageReactionCountsSelector(messageId));

  const styles = createStyles(theme);

  // Check if there are any reactions to display
  const hasReactions = Object.keys(reactionCounts).length > 0;

  if (!hasReactions) {
    return null;
  }

  // Check if current user has reacted with a specific type
  const hasUserReacted = (reactionType) => {
    return reactions[reactionType]?.includes(currentUserId) || false;
  };

  const handleReactionPress = (reactionType) => {
    const userHasReacted = hasUserReacted(reactionType);
    onReactionPress(messageId, reactionType, !userHasReacted);
  };

  return (
    <View style={styles.container}>
      {REACTION_ORDER.map(reactionType => {
        const count = reactionCounts[reactionType];
        if (!count || count === 0) return null;

        const userHasReacted = hasUserReacted(reactionType);

        return (
          <TouchableOpacity
            key={reactionType}
            style={[
              styles.reactionButton,
              userHasReacted && styles.reactionButtonActive
            ]}
            onPress={() => handleReactionPress(reactionType)}
            activeOpacity={0.7}
            hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
          >
            <Text style={styles.emoji}>
              {REACTION_EMOJIS[reactionType]}
            </Text>
          </TouchableOpacity>
        );
      })}
    </View>
  );
}

const createStyles = (theme) => StyleSheet.create({
  container: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 4,
    marginHorizontal: 8,
  },
  reactionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginRight: 6,
    marginBottom: 4,
    borderWidth: 1,
    borderColor: theme.colors.outline,
    minHeight: 28,
  },
  reactionButtonActive: {
    backgroundColor: theme.colors.primaryContainer,
    borderColor: theme.colors.primary,
  },
  emoji: {
    fontSize: 14,
    marginRight: 4,
  },
  count: {
    fontSize: 12,
    fontWeight: '600',
    color: theme.colors.onSurface,
    minWidth: 12,
    textAlign: 'center',
  },
  countActive: {
    color: theme.colors.primary,
  },
});

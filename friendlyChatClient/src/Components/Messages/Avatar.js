import React from 'react';
import {Image, StyleSheet} from 'react-native';
import {BigHead} from 'react-native-bigheads';

export default function Avatar(props) {
  const any =
    props.avatar ||
    (props.gender === 'f'
      ? require('../../assets/images/profile-female.png')
      : props.gender === 'm'
      ? require('../../assets/images/profile-male.png')
      : require('../../assets/images/profile-any.png'));
  return props.avatar ? (
    <BigHead
      {...props.avatar}
      hatColor="white"
      lipColor="pink"
      lashes={false}
      graphic="react"
      bgShape="circle"
      bgColor="blue"
      body={props.gender === 'm' ? 'chest' : 'breasts'}
      size={60}
    />
  ) : (
    <Image source={any} resizeMode="cover" style={styles.avatar} />
  );
}
const styles = StyleSheet.create({
  avatar: {
    width: 50,
    height: 50,
    alignSelf: 'center',
    borderRadius: 25,
    marginHorizontal: 5,
  },
});

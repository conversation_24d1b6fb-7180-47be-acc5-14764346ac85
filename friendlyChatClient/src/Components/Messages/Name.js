import React from 'react';
import {Text, StyleSheet} from 'react-native';

export default function Name(props) {
  const getName = () => {
    if (props.isSystemMessage) {
      return 'System';
    }
    if (props.partner && props.partner.name) {
      return props.partner.name;
    }
    return 'No Name';
  };
  return <Text style={[props.style, styles.font]}>{getName()}</Text>;
}
const styles = StyleSheet.create({font: {fontSize: 12}});

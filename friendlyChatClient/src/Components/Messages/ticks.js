import React from 'react';
import {Text, View} from 'react-native';
import MessageStatus from '../../Constants/MessageStatus';

export default function Ticks(props) {
  return (
    <View>
      {props.status === MessageStatus.sent && (
        <Text style={props.style}>✓</Text>
      )}
      {props.status === MessageStatus.delivered && (
        <Text style={props.style}>✓✓</Text>
      )}
      {props.status === MessageStatus.pending && (
        <Text style={props.style}>🕓</Text>
      )}
    </View>
  );
}

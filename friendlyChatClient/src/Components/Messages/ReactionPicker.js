import React, { useRef, useEffect } from 'react';
import { View, TouchableOpacity, Text, Animated, StyleSheet, Dimensions } from 'react-native';
import { useTheme } from 'react-native-paper';

const { width: screenWidth } = Dimensions.get('window');

// Reaction types and their emojis
const REACTIONS = [
  { type: 'like', emoji: '👍', label: 'Like' },
  { type: 'heart', emoji: '❤️', label: 'Love' },
  { type: 'laugh', emoji: '😂', label: 'Laugh' },
  { type: 'wow', emoji: '😮', label: 'Wow' }
];

export default function ReactionPicker({ 
  visible, 
  onReactionSelect, 
  onClose, 
  position = { x: 0, y: 0 },
  messageId 
}) {
  const theme = useTheme();
  const scaleAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(-20)).current;
  
  const styles = createStyles(theme);

  useEffect(() => {
    if (visible) {
      // Show animation
      Animated.parallel([
        Animated.spring(scaleAnim, {
          toValue: 1,
          useNativeDriver: true,
          tension: 300,
          friction: 20,
        }),
        Animated.timing(opacityAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.spring(slideAnim, {
          toValue: 0,
          useNativeDriver: true,
          tension: 300,
          friction: 20,
        }),
      ]).start();
    } else {
      // Hide animation
      Animated.parallel([
        Animated.spring(scaleAnim, {
          toValue: 0,
          useNativeDriver: true,
          tension: 300,
          friction: 20,
        }),
        Animated.timing(opacityAnim, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.spring(slideAnim, {
          toValue: -20,
          useNativeDriver: true,
          tension: 300,
          friction: 20,
        }),
      ]).start();
    }
  }, [visible, scaleAnim, opacityAnim, slideAnim]);

  const handleReactionPress = (reactionType) => {
    // Add a small haptic feedback-like animation
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 1.1,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start(() => {
      onReactionSelect(messageId, reactionType);
      onClose();
    });
  };

  if (!visible) {
    return null;
  }

  // Calculate position to keep picker on screen
  const pickerWidth = 240;
  const adjustedX = Math.min(position.x, screenWidth - pickerWidth - 20);

  return (
    <View style={[styles.overlay]} pointerEvents="box-none">
      <TouchableOpacity 
        style={styles.backdrop} 
        onPress={onClose}
        activeOpacity={1}
      />
      <Animated.View
        style={[
          styles.container,
          {
            left: adjustedX,
            top: position.y - 60, // Position above the message
            opacity: opacityAnim,
            transform: [
              { scale: scaleAnim },
              { translateY: slideAnim }
            ]
          }
        ]}
      >
        <View style={styles.picker}>
          {REACTIONS.map((reaction, index) => (
            <TouchableOpacity
              key={reaction.type}
              style={[
                styles.reactionButton,
                index === 0 && styles.firstButton,
                index === REACTIONS.length - 1 && styles.lastButton
              ]}
              onPress={() => handleReactionPress(reaction.type)}
              activeOpacity={0.7}
            >
              <Text style={styles.emoji}>{reaction.emoji}</Text>
            </TouchableOpacity>
          ))}
        </View>
        <View style={styles.arrow} />
      </Animated.View>
    </View>
  );
}

const createStyles = (theme) => StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  container: {
    position: 'absolute',
    alignItems: 'center',
  },
  picker: {
    flexDirection: 'row',
    backgroundColor: theme.colors.surface,
    borderRadius: 25,
    paddingHorizontal: 8,
    paddingVertical: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 12,
    borderWidth: 1,
    borderColor: theme.colors.outline,
  },
  reactionButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 2,
    backgroundColor: 'transparent',
  },
  firstButton: {
    marginLeft: 0,
  },
  lastButton: {
    marginRight: 0,
  },
  emoji: {
    fontSize: 24,
  },
  arrow: {
    width: 0,
    height: 0,
    backgroundColor: 'transparent',
    borderStyle: 'solid',
    borderLeftWidth: 8,
    borderRightWidth: 8,
    borderTopWidth: 8,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderTopColor: theme.colors.surface,
    marginTop: -1,
  },
});

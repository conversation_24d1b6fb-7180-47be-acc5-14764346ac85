import React from 'react';
import { StyleSheet, View, Text, TouchableOpacity, Linking } from 'react-native';
import { useTheme } from 'react-native-paper';
import analytics from '@react-native-firebase/analytics';

export const AppCard = ({ icon, title, description, packageId }) => {
  const theme = useTheme();

  const openAppStore = async () => {
    const url = `https://play.google.com/store/apps/details?id=${packageId}`;

    // Log analytics event
    await analytics().logEvent('app_promo_clicked', {
      app_name: title,
      package_id: packageId,
      source: 'about_page',
    });

    // Open Play Store
    Linking.openURL(url).catch(err =>
      console.error('Failed to open URL:', err),
    );
  };
  const styles = makeStyles(theme);

  return (
    <TouchableOpacity
      style={styles.appCard}
      onPress={openAppStore}
      activeOpacity={0.8}>
      <View style={styles.appCardContent}>
        <Text style={styles.appIcon}>{icon}</Text>
        <View style={styles.appInfo}>
          <Text style={styles.appTitle}>{title}</Text>
          <Text style={styles.appDescription}>{description}</Text>
        </View>
        <View style={styles.getAppButton}>
          <Text style={styles.getAppButtonText}>Get App</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const makeStyles = (theme) => StyleSheet.create({
  appCard: {
    backgroundColor: theme.colors.cardBackground,
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    shadowColor: theme.colors.shadow || '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  appCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  appIcon: {
    fontSize: 32,
    marginRight: 16,
    width: 50,
    textAlign: 'center',
  },
  appInfo: {
    flex: 1,
    marginRight: 12,
  },
  appTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 4,
  },
  appDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    lineHeight: 20,
  },
  getAppButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 20,
    shadowColor: theme.colors.primary,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 3,
  },
  getAppButtonText: {
    color: theme.colors.onPrimary,
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
});
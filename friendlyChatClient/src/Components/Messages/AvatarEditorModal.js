import React, { useState, useCallback } from 'react';
import {
    Modal,
    View,
    StyleSheet,
    TouchableOpacity,
    ScrollView,
    Dimensions,
} from 'react-native';
import {
    Text,
    Button,
    Portal,
    useTheme,
    Surface,
    Chip,
} from 'react-native-paper';
import { BigHead } from 'react-native-bigheads';
import {
    accessoryMap,
    clothingMap,
    eyebrowsMap,
    eyesMap,
    facialHairMap,
    hairMap,
    hatMap,
    mouthsMap,
    theme as bigHeadTheme,
} from 'react-native-bigheads';

const { width, height } = Dimensions.get('window');

const CATEGORIES = [
    { key: 'accessory', label: 'Accessory', map: accessoryMap },
    { key: 'clothing', label: 'Clothing', map: clothingMap },
    { key: 'clothingColor', label: 'Clothing Color', map: bigHeadTheme.colors.clothing },
    { key: 'eyebrows', label: 'Eyebrows', map: eyebrowsMap },
    { key: 'eyes', label: 'Eyes', map: eyesMap },
    { key: 'facialHair', label: 'Facial Hair', map: facialHairMap },
    { key: 'hair', label: 'Hair', map: hairMap },
    { key: 'hairColor', label: 'Hair Color', map: bigHeadTheme.colors.hair },
    { key: 'hat', label: 'Hat', map: hatMap },
    { key: 'mouth', label: 'Mouth', map: mouthsMap },
    { key: 'skinTone', label: 'Skin Tone', map: bigHeadTheme.colors.skin },
];

export default function AvatarEditorModal({ visible, onDismiss, avatar, onAvatarChange, gender }) {
    const theme = useTheme();
    const [activeCategory, setActiveCategory] = useState('accessory');

    const getTitle = (text) => {
        const result = text.replace(/([A-Z])/g, ' $1');
        return result.charAt(0).toUpperCase() + result.slice(1);
    };

    const handleItemSelect = (category, item) => {
        onAvatarChange({ ...avatar, [category]: item });
    };

    const randomizeAvatar = () => {
        const newAvatar = {
            accessory: selectRandomKey(accessoryMap),
            clothing: selectRandomKey(clothingMap),
            clothingColor: selectRandomKey(bigHeadTheme.colors.clothing),
            eyebrows: selectRandomKey(eyebrowsMap),
            eyes: selectRandomKey(eyesMap),
            facialHair: selectRandomKey(facialHairMap),
            hair: selectRandomKey(hairMap),
            hairColor: selectRandomKey(bigHeadTheme.colors.hair),
            hat: selectRandomKey(hatMap),
            mouth: selectRandomKey(mouthsMap),
            skinTone: selectRandomKey(bigHeadTheme.colors.skin),
        };
        onAvatarChange(newAvatar);
    };

    const selectRandomKey = (object) => {
        return Object.keys(object)[
            Math.floor(Math.random() * Object.keys(object).length)
        ];
    };

    const renderCategoryItems = () => {
        const category = CATEGORIES.find(cat => cat.key === activeCategory);
        if (!category) return null;

        return (
            <ScrollView
                contentContainerStyle={styles.itemsContainer}
                showsVerticalScrollIndicator={false}
                style={styles.itemsScrollView}
            >
                {Object.entries(category.map).map(([key, value]) => {
                    if (key.startsWith('none') && key.length === 5) return null;

                    const isSelected = avatar[category.key] === key;

                    return (
                        <TouchableOpacity
                            key={key}
                            style={[
                                styles.itemCard,
                                isSelected && styles.selectedItemCard,
                                {
                                    backgroundColor: theme.colors.surface,
                                    borderColor: isSelected ? theme.colors.primary : theme.colors.outline
                                }
                            ]}
                            onPress={() => handleItemSelect(category.key, key)}
                            activeOpacity={0.7}
                        >
                            <View style={styles.previewContainer}>
                                <BigHead
                                    {...avatar}
                                    {...{ [category.key]: key }}
                                    hatColor="white"
                                    lipColor="pink"
                                    lashes={false}
                                    graphic="react"
                                    bgShape="circle"
                                    bgColor={theme.colors.primary}
                                    body={gender === 'm' ? 'chest' : 'breasts'}
                                    size={50}
                                />
                            </View>
                            <Text
                                variant="bodySmall"
                                style={[
                                    styles.itemText,
                                    isSelected && { color: theme.colors.primary, fontWeight: '600' },
                                    { color: theme.colors.onSurface }
                                ]}
                                numberOfLines={2}
                            >
                                {getTitle(key)}
                            </Text>
                        </TouchableOpacity>
                    );
                })}
            </ScrollView>
        );
    };

    return (
        <Portal>
            <Modal
                visible={visible}
                onDismiss={onDismiss}
                animationType="slide"
                transparent={true}
            >
                <View style={styles.modalOverlay}>
                    <Surface style={[styles.modalContent, { backgroundColor: theme.colors.surface }]} elevation={5}>
                        {/* Header */}
                        <View style={styles.header}>
                            <Text variant="headlineSmall" style={[styles.title, { color: theme.colors.onSurface }]}>
                                Customize Avatar
                            </Text>
                            <Button
                                mode="text"
                                onPress={onDismiss}
                                icon="close"
                                compact
                                contentStyle={styles.closeButtonContent}
                            />
                        </View>

                        {/* Avatar Preview */}
                        <View style={[styles.avatarPreview, { backgroundColor: theme.colors.surfaceVariant }]}>
                            <BigHead
                                {...avatar}
                                hatColor="white"
                                lipColor="pink"
                                lashes={false}
                                graphic="react"
                                bgShape="circle"
                                bgColor={theme.colors.primary}
                                body={gender === 'm' ? 'chest' : 'breasts'}
                                size={100}
                            />
                        </View>

                        {/* Category Tabs */}
                        <ScrollView
                            horizontal
                            showsHorizontalScrollIndicator={false}
                            contentContainerStyle={styles.tabsContainer}
                            style={styles.tabsScrollView}
                        >
                            {CATEGORIES.map((category) => (
                                <Chip
                                    key={category.key}
                                    selected={activeCategory === category.key}
                                    onPress={() => setActiveCategory(category.key)}
                                    style={[
                                        styles.tabChip,
                                        {
                                            backgroundColor: activeCategory === category.key
                                                ? theme.colors.primary
                                                : theme.colors.surfaceVariant
                                        }
                                    ]}
                                    textStyle={[
                                        styles.tabText,
                                        {
                                            color: activeCategory === category.key
                                                ? theme.colors.onPrimary
                                                : theme.colors.onSurfaceVariant
                                        }
                                    ]}
                                    showSelectedOverlay={false}
                                >
                                    {category.label}
                                </Chip>
                            ))}
                        </ScrollView>

                        {/* Items Grid */}
                        <View style={styles.itemsWrapper}>
                            {renderCategoryItems()}
                        </View>

                        {/* Randomize Button */}
                        <Button
                            mode="contained"
                            icon="shuffle-variant"
                            onPress={randomizeAvatar}
                            style={styles.randomizeButton}
                            contentStyle={styles.buttonContent}
                        >
                            Randomize Avatar
                        </Button>
                    </Surface>
                </View>
            </Modal>
        </Portal>
    );
}

const styles = StyleSheet.create({
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.6)',
        justifyContent: 'center',
        alignItems: 'center',
        // padding: 16,
    },
    modalContent: {
        width: '90%',
        maxHeight: height * 0.85,
        borderRadius: 16,
        padding: 20,
        paddingBottom: 16,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 20,
    },
    title: {
        flex: 1,
        fontWeight: '600',
    },
    closeButtonContent: {
        paddingHorizontal: 8,
    },
    avatarPreview: {
        alignItems: 'center',
        marginBottom: 20,
        paddingVertical: 20,
        paddingHorizontal: 16,
        borderRadius: 16,
    },
    tabsScrollView: {
        maxHeight: 60,
        marginBottom: 16,
    },
    tabsContainer: {
        paddingHorizontal: 4,
        gap: 8,
    },
    tabChip: {
        marginHorizontal: 0,
        borderRadius: 20,
        height: 36,
        justifyContent: 'center',
        minWidth: 80,
        paddingHorizontal: 12,
    },
    tabText: {
        fontSize: 12,
        fontWeight: '500',
        textAlign: 'center',
    },
    itemsWrapper: {
        flex: 1,
        minHeight: 200,
        maxHeight: 300,
    },
    itemsScrollView: {
        flex: 1,
    },
    itemsContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
        paddingHorizontal: 4,
        paddingBottom: 16,
    },
    itemCard: {
        width: (width - 120) / 3,
        minWidth: 80,
        maxWidth: 100,
        aspectRatio: 0.8,
        alignItems: 'center',
        justifyContent: 'center',
        padding: 8,
        marginBottom: 12,
        borderRadius: 12,
        borderWidth: 1.5,
        elevation: 1,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.1,
        shadowRadius: 2,
    },
    selectedItemCard: {
        borderWidth: 2,
        elevation: 2,
        shadowOpacity: 0.15,
    },
    previewContainer: {
        marginBottom: 6,
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    itemText: {
        fontSize: 10,
        textAlign: 'center',
        lineHeight: 12,
        paddingHorizontal: 2,
    },
    randomizeButton: {
        marginTop: 16,
        alignSelf: 'center',
        borderRadius: 24,
        minWidth: 160,
    },
    buttonContent: {
        paddingHorizontal: 24,
        paddingVertical: 8,
    },
});
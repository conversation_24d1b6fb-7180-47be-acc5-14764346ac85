import React, {useCallback} from 'react';
import {StyleSheet, Text} from 'react-native';

import {List} from 'react-native-paper';
import ActionInputMenu from './Menu';

export default function AvatarListItem(props) {
  let getTitle = text => {
    const result = text.replace(/([A-Z])/g, ' $1');
    const finalResult = result.charAt(0).toUpperCase() + result.slice(1);
    return finalResult;
  };
  let right = useCallback(
    () => <Text style={styles.settingAnchor}>{props.value.toString()}</Text>,
    [props.value],
  );
  const menuAnchor = useCallback(
    () => (
      <List.Item
        style={styles.linePadding}
        titleStyle={styles.titleStyle}
        title={getTitle(props.propName)}
        right={right}
      />
    ),
    [props.propName, right],
  );
  return (
    <ActionInputMenu
      menuAnchor={menuAnchor}
      menuItems={Object.keys(props.list).reduce((prevItem, item) => {
        if (item.startsWith('none') && item.length === 5) {
          return prevItem;
        }
        prevItem.push([item, () => props.action(props.propName, item)]);
        return prevItem;
      }, [])}
    />
  );
}

const styles = StyleSheet.create({
  settingAnchor: {
    alignSelf: 'center',
    marginTop: 6,
    color: 'gray',
  },
  linePadding: {
    padding: 0,
  },
  titleStyle: {color: 'black'},
});

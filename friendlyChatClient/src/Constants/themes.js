import { MD3LightTheme, MD3DarkTheme, configureFonts } from 'react-native-paper';
import { Platform } from 'react-native';

const baseFont = Platform.select({
  ios: 'Poppins-Regular',
  android: 'PoppinsRegular-B2Bw',
});

const baseFontBold = Platform.select({
  ios: 'Poppins-Bold',
  android: 'PoppinsBold-GdJA',
});

const fontConfig = {
  displayLarge: {
    ...MD3LightTheme.fonts.displayLarge,
    fontFamily: baseFontBold,
  },
  displayMedium: {
    ...MD3LightTheme.fonts.displayMedium,
    fontFamily: baseFontBold,
  },
  displaySmall: {
    ...MD3LightTheme.fonts.displaySmall,
    fontFamily: baseFontBold,
  },
  headlineLarge: {
    ...MD3LightTheme.fonts.headlineLarge,
    fontFamily: baseFontBold,
  },
  headlineMedium: {
    ...MD3LightTheme.fonts.headlineMedium,
    fontFamily: baseFontBold,
  },
  headlineSmall: {
    ...MD3LightTheme.fonts.headlineSmall,
    fontFamily: baseFontBold,
  },
  titleLarge: {
    ...MD3LightTheme.fonts.titleLarge,
    fontFamily: baseFontBold,
  },
  titleMedium: {
    ...MD3LightTheme.fonts.titleMedium,
    fontFamily: baseFontBold,
  },
  titleSmall: {
    ...MD3LightTheme.fonts.titleSmall,
    fontFamily: baseFontBold,
  },
  bodyLarge: {
    ...MD3LightTheme.fonts.bodyLarge,
    fontFamily: baseFont,
  },
  bodyMedium: {
    ...MD3LightTheme.fonts.bodyMedium,
    fontFamily: baseFont,
  },
  bodySmall: {
    ...MD3LightTheme.fonts.bodySmall,
    fontFamily: baseFont,
  },
  labelLarge: {
    ...MD3LightTheme.fonts.labelLarge,
    fontFamily: baseFont,
  },
  labelMedium: {
    ...MD3LightTheme.fonts.labelMedium,
    fontFamily: baseFont,
  },
  labelSmall: {
    ...MD3LightTheme.fonts.labelSmall,
    fontFamily: baseFont,
  },
};

// Base theme structure
const baseTheme = {
  fonts: configureFonts({ config: fontConfig }),
  roundness: 12,
  shadow: {
    small: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 3,
      elevation: 2,
    },
  },
  spacing: {
    xsmall: 4,
    small: 8,
    medium: 16,
    large: 24,
    xlarge: 32,
    xxlarge: 48,
  },
  typography: {
    heading: {
      fontSize: 28,
      fontWeight: '700',
      letterSpacing: 0.25,
    },
    subheading: {
      fontSize: 22,
      fontWeight: '600',
      letterSpacing: 0.15,
    },
    body: {
      fontSize: 16,
      fontWeight: '400',
      letterSpacing: 0.5,
    },
    caption: {
      fontSize: 14,
      fontWeight: '400',
      letterSpacing: 0.4,
    },
    value: {
      fontSize: 42,
      fontWeight: '700',
      letterSpacing: -0.5,
    },
  },
  borderRadius: {
    small: 8,
    medium: 12,
    large: 16,
    pill: 50,
  },
};

// Color scheme definitions
export const colorSchemes = {
  blue: {
    light: {
      primary: '#2196f3',
      secondary: '#5276AC',
      tertiary: '#f7941d',
      accent: '#03DAC6',
      background: '#E3F2FD', // Light blue background
      surface: '#BBDEFB', // Lighter blue surface
    },
    dark: {
      primary: '#64B5F6',
      secondary: '#7B9BC4',
      tertiary: '#FFB74D',
      accent: '#18FFFF',
      background: '#0D47A1', // Deep blue background
      surface: '#1565C0', // Medium blue surface
    },
  },
  purple: {
    light: {
      primary: '#9C27B0',
      secondary: '#7B1FA2',
      tertiary: '#E91E63',
      accent: '#FF4081',
      background: '#F3E5F5', // Light purple background
      surface: '#E1BEE7', // Lighter purple surface
    },
    dark: {
      primary: '#CE93D8',
      secondary: '#BA68C8',
      tertiary: '#F48FB1',
      accent: '#FF80AB',
      background: '#4A148C', // Deep purple background
      surface: '#6A1B9A', // Medium purple surface
    },
  },
  green: {
    light: {
      primary: '#4CAF50',
      secondary: '#388E3C',
      tertiary: '#8BC34A',
      accent: '#00E676',
      background: '#E8F5E8', // Light green background
      surface: '#C8E6C9', // Lighter green surface
    },
    dark: {
      primary: '#81C784',
      secondary: '#66BB6A',
      tertiary: '#AED581',
      accent: '#69F0AE',
      background: '#1B5E20', // Deep green background
      surface: '#2E7D32', // Medium green surface
    },
  },
  orange: {
    light: {
      primary: '#FF9800',
      secondary: '#F57C00',
      tertiary: '#FF5722',
      accent: '#FF6D00',
      background: '#FFF3E0', // Light orange background
      surface: '#FFE0B2', // Lighter orange surface
    },
    dark: {
      primary: '#FFB74D',
      secondary: '#FFA726',
      tertiary: '#FF8A65',
      accent: '#FF9100',
      background: '#E65100', // Deep orange background
      surface: '#EF6C00', // Medium orange surface
    },
  },
  pink: {
    light: {
      primary: '#E91E63',
      secondary: '#C2185B',
      tertiary: '#F06292',
      accent: '#FF4081',
      background: '#FCE4EC', // Light pink background
      surface: '#F8BBD0', // Lighter pink surface
    },
    dark: {
      primary: '#F48FB1',
      secondary: '#F06292',
      tertiary: '#F8BBD9',
      accent: '#FF80AB',
      background: '#880E4F', // Deep pink background
      surface: '#AD1457', // Medium pink surface
    },
  },
};

// Create theme variants
const createTheme = (colorScheme, isDark = false) => {
  const baseColors = isDark ? MD3DarkTheme.colors : MD3LightTheme.colors;
  const schemeColors = colorSchemes[colorScheme][isDark ? 'dark' : 'light'];

  return {
    ...baseTheme,
    ...(isDark ? MD3DarkTheme : MD3LightTheme),
    colors: {
      ...baseColors,
      ...schemeColors,
      // Custom colors for the app
      backgroundColor: schemeColors.background,
      background: isDark ? '#121212' : '#FFFFFF',
      surface: schemeColors.surface,
      text: isDark ? '#FFFFFF' : '#000000',
      textSecondary: isDark ? '#B0B0B0' : '#666666',
      disabled: isDark ? '#666666' : '#949494',
      placeholder: isDark ? '#888888' : '#B4B3B3',
      backdrop: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.5)',
      notification: isDark ? '#FF6B6B' : '#f50057',
      border: isDark ? '#333333' : '#E0E0E0',
      cardBackground: isDark ? '#1E1E1E' : '#FFFFFF',
      // Chat-specific colors
      inputText: isDark ? '#FFFFFF' : '#000000',
      strangerChatting: isDark ? '#2A2A2A' : '#E3E3E3',
      userChatting: schemeColors.tertiary,
      icon: schemeColors.primary,
      textInputBackground: isDark ? '#2A2A2A' : '#E3E3E3',
      actionBtn: schemeColors.secondary,
    },
  };
};

// Export all theme variants
export const themes = {
  blue: {
    light: createTheme('blue', false),
    dark: createTheme('blue', true),
  },
  purple: {
    light: createTheme('purple', false),
    dark: createTheme('purple', true),
  },
  green: {
    light: createTheme('green', false),
    dark: createTheme('green', true),
  },
  orange: {
    light: createTheme('orange', false),
    dark: createTheme('orange', true),
  },
  pink: {
    light: createTheme('pink', false),
    dark: createTheme('pink', true),
  },
};

// Default theme (blue light)
export const defaultTheme = themes.blue.light;

// Helper function to get theme
export const getTheme = (colorScheme = 'blue', isDark = false) => {
  return themes[colorScheme]?.[isDark ? 'dark' : 'light'] || defaultTheme;
};

export default themes;

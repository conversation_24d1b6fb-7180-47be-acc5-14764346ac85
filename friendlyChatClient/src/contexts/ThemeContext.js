import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Appearance } from 'react-native';
import { getTheme, themes, defaultTheme } from '../Constants/themes';

const ThemeContext = createContext();

const THEME_STORAGE_KEY = '@friendly_theme_preferences';

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export const ThemeProvider = ({ children }) => {
  const [colorScheme, setColorScheme] = useState('blue');
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [isSystemTheme, setIsSystemTheme] = useState(true);
  const [currentTheme, setCurrentTheme] = useState(defaultTheme);
  const [isLoading, setIsLoading] = useState(true);

  // Load theme preferences from storage
  const loadThemePreferences = useCallback(async () => {
    try {
      const storedPreferences = await AsyncStorage.getItem(THEME_STORAGE_KEY);
      if (storedPreferences) {
        const preferences = JSON.parse(storedPreferences);
        setColorScheme(preferences.colorScheme || 'blue');
        setIsSystemTheme(preferences.isSystemTheme !== false); // Default to true
        
        if (preferences.isSystemTheme !== false) {
          // Use system theme
          const systemColorScheme = Appearance.getColorScheme();
          setIsDarkMode(systemColorScheme === 'dark');
        } else {
          // Use manual theme setting
          setIsDarkMode(preferences.isDarkMode || false);
        }
      } else {
        // First time - use system theme
        const systemColorScheme = Appearance.getColorScheme();
        setIsDarkMode(systemColorScheme === 'dark');
      }
    } catch (error) {
      console.error('Error loading theme preferences:', error);
      // Fallback to system theme
      const systemColorScheme = Appearance.getColorScheme();
      setIsDarkMode(systemColorScheme === 'dark');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Save theme preferences to storage
  const saveThemePreferences = useCallback(async (preferences) => {
    try {
      await AsyncStorage.setItem(THEME_STORAGE_KEY, JSON.stringify(preferences));
    } catch (error) {
      console.error('Error saving theme preferences:', error);
    }
  }, []);

  // Update current theme when preferences change
  useEffect(() => {
    const newTheme = getTheme(colorScheme, isDarkMode);
    setCurrentTheme(newTheme);
  }, [colorScheme, isDarkMode]);

  // Handle system theme changes
  useEffect(() => {
    if (isSystemTheme) {
      const subscription = Appearance.addChangeListener(({ colorScheme: systemColorScheme }) => {
        setIsDarkMode(systemColorScheme === 'dark');
      });

      return () => subscription?.remove();
    }
  }, [isSystemTheme]);

  // Load preferences on mount
  useEffect(() => {
    loadThemePreferences();
  }, [loadThemePreferences]);

  // Theme switching functions
  const switchColorScheme = useCallback(async (newColorScheme) => {
    if (themes[newColorScheme]) {
      setColorScheme(newColorScheme);
      const preferences = {
        colorScheme: newColorScheme,
        isDarkMode,
        isSystemTheme,
      };
      await saveThemePreferences(preferences);
    }
  }, [isDarkMode, isSystemTheme, saveThemePreferences]);

  const toggleDarkMode = useCallback(async () => {
    const newIsDarkMode = !isDarkMode;
    setIsDarkMode(newIsDarkMode);
    setIsSystemTheme(false); // Disable system theme when manually toggling
    
    const preferences = {
      colorScheme,
      isDarkMode: newIsDarkMode,
      isSystemTheme: false,
    };
    await saveThemePreferences(preferences);
  }, [isDarkMode, colorScheme, saveThemePreferences]);

  const enableSystemTheme = useCallback(async () => {
    setIsSystemTheme(true);
    const systemColorScheme = Appearance.getColorScheme();
    setIsDarkMode(systemColorScheme === 'dark');
    
    const preferences = {
      colorScheme,
      isDarkMode: systemColorScheme === 'dark',
      isSystemTheme: true,
    };
    await saveThemePreferences(preferences);
  }, [colorScheme, saveThemePreferences]);

  const setManualTheme = useCallback(async (newColorScheme, newIsDarkMode) => {
    setColorScheme(newColorScheme);
    setIsDarkMode(newIsDarkMode);
    setIsSystemTheme(false);
    
    const preferences = {
      colorScheme: newColorScheme,
      isDarkMode: newIsDarkMode,
      isSystemTheme: false,
    };
    await saveThemePreferences(preferences);
  }, [saveThemePreferences]);

  // Get available color schemes
  const getAvailableColorSchemes = useCallback(() => {
    return Object.keys(themes);
  }, []);

  // Get theme info
  const getThemeInfo = useCallback(() => {
    return {
      colorScheme,
      isDarkMode,
      isSystemTheme,
      themeName: `${colorScheme} ${isDarkMode ? 'dark' : 'light'}`,
    };
  }, [colorScheme, isDarkMode, isSystemTheme]);

  const contextValue = {
    // Current theme
    theme: currentTheme,
    
    // Theme state
    colorScheme,
    isDarkMode,
    isSystemTheme,
    isLoading,
    
    // Theme actions
    switchColorScheme,
    toggleDarkMode,
    enableSystemTheme,
    setManualTheme,
    
    // Utility functions
    getAvailableColorSchemes,
    getThemeInfo,
    
    // Direct theme access
    themes,
    getTheme,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

export default ThemeContext;

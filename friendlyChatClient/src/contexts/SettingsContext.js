import AsyncStorage from '@react-native-async-storage/async-storage';
import React, { Component, createContext } from 'react';

const SettingsContext = createContext();

export default SettingsContext;

export class SettingsProvider extends Component {
  constructor(props) {
    console.log('settings provider');
    super(props);
    AsyncStorage.getItem('settings').then(data => {
      if (data) {
        const parsedData = JSON.parse(data);
        this.setState({
          ...parsedData,
          // Ensure theme preferences have default values
          themePreferences: {
            colorScheme: 'blue',
            isDarkMode: false,
            isSystemTheme: true,
            ...parsedData.themePreferences,
          },
        });
      }
    });
  }
  state = {
    name: undefined,
    gender: undefined,
    avatar: undefined,
    // Theme-related settings
    themePreferences: {
      colorScheme: 'blue',
      isDarkMode: false,
      isSystemTheme: true,
    },
  };

  setData = data => {
    this.setState(data, () => {
      // Save to AsyncStorage whenever settings change
      AsyncStorage.setItem('settings', JSON.stringify(this.state));
    });
  };

  // Helper method to update theme preferences specifically
  setThemePreferences = (themePreferences) => {
    this.setState(
      {
        themePreferences: {
          ...this.state.themePreferences,
          ...themePreferences,
        },
      },
      () => {
        AsyncStorage.setItem('settings', JSON.stringify(this.state));
      }
    );
  };

  render() {
    return (
      <SettingsContext.Provider
        value={{
          ...this.state,
          setSettings: this.setData,
          setThemePreferences: this.setThemePreferences,
        }}>
        {this.props.children}
      </SettingsContext.Provider>
    );
  }
}

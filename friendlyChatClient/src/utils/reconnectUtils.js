import SocketService from '../services/socketIoService';
import { showInterstitial } from '../Components/ads/Interstitials';
import analytics from '@react-native-firebase/analytics';
import remoteConfig from '@react-native-firebase/remote-config';

// Unified reconnect logic with ad handling
export const reconnectWithAdLogic = (partners, isAdFree) => {
    const AdInterval = remoteConfig().getValue('AdInterval').asNumber();
    analytics().logEvent('next_user');
    const partnersLenght = partners.length;

    // Use cached ad-free status - no async call needed!
    if (isAdFree) {
        // User has paid, skip ad
        console.log('User has ad-free status, skipping ad');
        SocketService.emit('reconnect');
    } else if (
        partnersLenght !== 0 &&
        Math.ceil(partnersLenght / 2) % AdInterval === 0
    ) {
        SocketService.emit('end');
        showInterstitial();
    } else {
        SocketService.emit('reconnect');
    }
};